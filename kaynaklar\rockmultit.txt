// ©Rocksody Multi
//@version=5
indicator(title="rawfile", shorttitle="Rock Multi ", overlay = true, max_boxes_count=500, max_labels_count=500, max_lines_count=500, max_bars_back=1000)
// Global settings {
timeonoff=input(false,title='Show Time Açılışlar',group='Global Settings')
Manyind=input(false,title="Divergence For Many Uyumsuzluklar",group='Global Settings')
ShowHHLL=input(false,title='HH, LL, LH, HL',group='Global Settings')
vwaponoff=input(false,title="VWAP ",group='Global Settings')
showmas=input(false, title='MA 50 & 200',group='Global Settings')
Poi=input(false, title='POI SUPPLY & DEMAND',group='Global Settings')
showPivot=input(false, title='Likidite Göster',group='Global Settings')
Trend=input(false, title='TrendLine',group='Global Settings')
showTimeLevels = input(false, title='Displacement', group='Global Settings')
// }
// Show Time {
f_create_level(_type, _start_condition, _active_condition, _global_level_array, _color, _line_width, _line_ext, _line_style, _label_size, _title, _iter, _is_enabled) =>
    var float _price = na
    var int _start_time = na
    var float _hh = na
    var float _ll = na
    var line _price_line = line.new(x1 = na, y1 = na, x2 = na, y2 = na, xloc = xloc.bar_time, color = _color, width = _line_width, style = _line_style)
    var label _price_label = label.new(x = na, y = na, xloc = xloc.bar_time, style = label.style_label_left, color = #00000000, size = _label_size, textcolor = _color)
    _end_time = int(time + _line_ext * ta.change(time))
    if _type == "open"
        if _start_condition
            _price := open
            _start_time := time
    else if _type == "high"
        if _start_condition
            _price := high
            _start_time := time
        else if _active_condition
            _price := math.max(_price, high)
    else if _type == "low"
        if _start_condition
            _price := low
            _start_time := time
        else if _active_condition
            _price := math.min(_price, low)
    else if _type == "eq"
        if _start_condition
            _hh := high
            _ll := low
            _price := math.avg(_hh, _ll)
            _start_time := time
        else if _active_condition
            _hh := math.max(_hh, high)
            _ll := math.min(_ll, low)
            _price := math.avg(_hh, _ll)
    float _price_val = _iter == 0 ? _price : ta.valuewhen(_start_condition, _price[1], _iter - 1)
    int _start_time_val = _iter == 0 ? _start_time : ta.valuewhen(_start_condition, _start_time[1], _iter - 1)
    _found_existing = array.indexof(_global_level_array, _price_val) > -1
    if _is_enabled and timeonoff
        if _found_existing
            line.set_xy1(_price_line, x = na, y = na)
            line.set_xy2(_price_line, x = na, y = na)
            label.set_xy(_price_label, x = na, y = na)
        else
            array.push(_global_level_array, _price_val)
            line.set_xy1(_price_line, x = _start_time_val, y = _price_val)
            line.set_xy2(_price_line, x = _end_time, y = _price_val)
            label.set_text(_price_label, text = _title + " : " + str.tostring(_price_val))
            label.set_xy(_price_label, x = _end_time, y = _price_val)
float[] global_open_array = array.new_float()
float[] global_high_array = array.new_float()
float[] global_low_array = array.new_float()
float[] global_eq_array = array.new_float()
new_H4 = ta.change(time("240")) != 0
new_day = ta.change(time("D")) != 0
new_week = ta.change(time("W")) != 0
new_month = ta.change(time("M")) != 0
new_quarter = ta.change(time("3M")) != 0
new_year = ta.change(time("12M")) != 0
is_monday = dayofweek == dayofweek.monday
inp_open_line_style =   input.string("Solid",   options = ["Solid", "Dotted", "Dashed"], title = "Open ", group = "Time Settings", inline='1')
inp_high_line_style =   input.string("Solid",   options = ["Solid", "Dotted", "Dashed"], title = " | High", group = "Time Settings", inline='1')
inp_low_line_style =    input.string("Solid",   options = ["Solid", "Dotted", "Dashed"], title = " | Low",  group = "Time Settings", inline='1')
inp_text_size =         input.string("Small",   options = ["Small", "Normal", "Large"],  title = "Text Size",       group = "Time Settings", inline='4')
inp_ext =               input.int(20, title = "Line Extension", group = "Time Settings", inline='4')
text_size = inp_text_size == "Small" ? size.small : inp_text_size == "Normal" ? size.normal : size.large
open_line_style = inp_open_line_style == "Solid" ? line.style_solid : inp_open_line_style == "Dotted" ? line.style_dotted : line.style_dashed
high_line_style = inp_high_line_style == "Solid" ? line.style_solid : inp_high_line_style == "Dotted" ? line.style_dotted : line.style_dashed
low_line_style  = inp_low_line_style  == "Solid" ? line.style_solid : inp_low_line_style  == "Dotted" ? line.style_dotted : line.style_dashed
inp_show_daily_open         = input.bool(true,  title = "Daily        |",group = "TIME OPEN-HIGH-LOW", inline = "5")
inp_show_daily_high  = input.bool(false,  title = "High |",      group = "TIME OPEN-HIGH-LOW", inline = "5")
inp_show_daily_low   = input.bool(false,  title = "Low |",       group = "TIME OPEN-HIGH-LOW", inline = "5")
inp_daily_col        = input.color(color.gray, title = "",       group = "TIME OPEN-HIGH-LOW", inline = "5")
inp_daily_line_width = input.int(1, title = "", minval = 1,      group = "TIME OPEN-HIGH-LOW", inline = "5")
daily_ok = timeframe.isintraday
f_create_level("open", new_day, not new_day, global_open_array, inp_daily_col, inp_daily_line_width, inp_ext, open_line_style, text_size, "DO", 0, inp_show_daily_open and daily_ok)
f_create_level("high", new_day, not new_day, global_high_array, inp_daily_col, inp_daily_line_width, inp_ext, high_line_style, text_size, "DH", 0, inp_show_daily_high and daily_ok)
f_create_level("low", new_day, not new_day, global_low_array, inp_daily_col, inp_daily_line_width, inp_ext, low_line_style, text_size, "DL", 0, inp_show_daily_low and daily_ok)
inp_show_prev_daily_open  = input.bool(false, title = "Prev Daily     |",  group = "PREV TIME OPEN-HIGH-LOW", inline = "6")
inp_show_prev_daily_high  = input.bool(false, title = "High |",             group = "PREV TIME OPEN-HIGH-LOW", inline = "6")
inp_show_prev_daily_low   = input.bool(false, title = "Low |",              group = "PREV TIME OPEN-HIGH-LOW", inline = "6")
inp_prev_daily_col        = input.color(color.gray, title = "",             group = "PREV TIME OPEN-HIGH-LOW", inline = "6")
inp_prev_daily_line_width = input.int(1, title = "", minval = 1,            group = "PREV TIME OPEN-HIGH-LOW", inline = "6")
f_create_level("open", new_day, not new_day, global_open_array, inp_prev_daily_col, inp_prev_daily_line_width, inp_ext, open_line_style, text_size, "PDO", 1, inp_show_prev_daily_open and daily_ok)
f_create_level("high", new_day, not new_day, global_high_array, inp_prev_daily_col, inp_prev_daily_line_width, inp_ext, high_line_style, text_size, "PDH", 1, inp_show_prev_daily_high and daily_ok)
f_create_level("low", new_day, not new_day, global_low_array, inp_prev_daily_col, inp_prev_daily_line_width, inp_ext, low_line_style, text_size, "PDL", 1, inp_show_prev_daily_low and daily_ok)
inp_show_weekly_open        = input.bool(true,  title = "Weekly      |", group = "TIME OPEN-HIGH-LOW", inline = "7")
inp_show_weekly_high  = input.bool(false, title = "High |",        group = "TIME OPEN-HIGH-LOW", inline = "7")
inp_show_weekly_low   = input.bool(false, title = "Low |",         group = "TIME OPEN-HIGH-LOW", inline = "7")
inp_weekly_col        = input.color(color.aqua, title = "",        group = "TIME OPEN-HIGH-LOW", inline = "7")
inp_weekly_line_width = input.int(2, title = "", minval = 1,       group = "TIME OPEN-HIGH-LOW", inline = "7")
weekly_ok = timeframe.isintraday or timeframe.isdaily
f_create_level("open", new_week, not new_week, global_open_array, inp_weekly_col, inp_weekly_line_width, inp_ext, open_line_style, text_size, "WO", 0, inp_show_weekly_open and weekly_ok)
f_create_level("high", new_week, not new_week, global_high_array, inp_weekly_col, inp_weekly_line_width, inp_ext, high_line_style, text_size, "WH", 0, inp_show_weekly_high and weekly_ok)
f_create_level("low", new_week, not new_week, global_low_array, inp_weekly_col, inp_weekly_line_width, inp_ext, low_line_style, text_size, "WL", 0, inp_show_weekly_low and weekly_ok)
inp_show_prev_weekly_open  = input.bool(false, title = "Prev Weekly   |", group = "PREV TIME OPEN-HIGH-LOW", inline = "8")
inp_show_prev_weekly_high  = input.bool(false, title = "High |",             group = "PREV TIME OPEN-HIGH-LOW", inline = "8")
inp_show_prev_weekly_low   = input.bool(false, title = "Low |",              group = "PREV TIME OPEN-HIGH-LOW", inline = "8")
inp_prev_weekly_col        = input.color(color.blue, title = "",             group = "PREV TIME OPEN-HIGH-LOW", inline = "8")
inp_prev_weekly_line_width = input.int(2, title = "", minval = 1,            group = "PREV TIME OPEN-HIGH-LOW", inline = "8")
f_create_level("open", new_week, not new_week, global_open_array, inp_prev_weekly_col, inp_prev_weekly_line_width, inp_ext, open_line_style, text_size, "PWO", 1, inp_show_prev_weekly_open and weekly_ok)
f_create_level("high", new_week, not new_week, global_high_array, inp_prev_weekly_col, inp_prev_weekly_line_width, inp_ext, high_line_style, text_size, "PWH", 1, inp_show_prev_weekly_high and weekly_ok)
f_create_level("low", new_week, not new_week, global_low_array, inp_prev_weekly_col, inp_prev_weekly_line_width, inp_ext, low_line_style, text_size, "PWL", 1, inp_show_prev_weekly_low and weekly_ok)
inp_show_monthly_open       = input.bool(true,  title = "Monthly     |",group = "TIME OPEN-HIGH-LOW", inline = "9")
inp_show_monthly_high  = input.bool(false, title = "High |",        group = "TIME OPEN-HIGH-LOW", inline = "9")
inp_show_monthly_low   = input.bool(false, title = "Low |",         group = "TIME OPEN-HIGH-LOW", inline = "9")
inp_monthly_col        = input.color(color.lime, title = "",        group = "TIME OPEN-HIGH-LOW", inline = "9")
inp_monthly_line_width = input.int(3, title = "", minval = 1,       group = "TIME OPEN-HIGH-LOW", inline = "9")
monthly_ok = timeframe.isintraday or timeframe.isdaily
f_create_level("open", new_month, not new_month, global_open_array, inp_monthly_col, inp_monthly_line_width, inp_ext, open_line_style, text_size, "MO", 0, inp_show_monthly_open and monthly_ok)
f_create_level("high", new_month, not new_month, global_high_array, inp_monthly_col, inp_monthly_line_width, inp_ext, high_line_style, text_size, "MH", 0, inp_show_monthly_high and monthly_ok)
f_create_level("low", new_month, not new_month, global_low_array, inp_monthly_col, inp_monthly_line_width, inp_ext, low_line_style, text_size, "ML", 0, inp_show_monthly_low and monthly_ok)
inp_show_prev_monthly_open  = input.bool(false, title = "Prev Monthly  |",  group = "PREV TIME OPEN-HIGH-LOW", inline = "10")
inp_show_prev_monthly_high  = input.bool(false, title = "High |",               group = "PREV TIME OPEN-HIGH-LOW", inline = "10")
inp_show_prev_monthly_low   = input.bool(false, title = "Low |",                group = "PREV TIME OPEN-HIGH-LOW", inline = "10")
inp_prev_monthly_col        = input.color(color.green, title = "",              group = "PREV TIME OPEN-HIGH-LOW", inline = "10")
inp_prev_monthly_line_width = input.int(3, title = "", minval = 1,              group = "PREV TIME OPEN-HIGH-LOW", inline = "10")
f_create_level("open", new_month, not new_month, global_open_array, inp_prev_monthly_col, inp_prev_monthly_line_width, inp_ext, open_line_style, text_size, "PMO", 1, inp_show_prev_monthly_open and monthly_ok)
f_create_level("high", new_month, not new_month, global_high_array, inp_prev_monthly_col, inp_prev_monthly_line_width, inp_ext, high_line_style, text_size, "PMH", 1, inp_show_prev_monthly_high and monthly_ok)
f_create_level("low", new_month, not new_month, global_low_array, inp_prev_monthly_col, inp_prev_monthly_line_width, inp_ext, low_line_style, text_size, "PML", 1, inp_show_prev_monthly_low and monthly_ok)
inp_show_quarterly_open  = input.bool(true,  title = "Quarterly    |",group = "TIME OPEN-HIGH-LOW", inline = "11")
inp_show_quarterly_high  = input.bool(false, title = "High |",          group = "TIME OPEN-HIGH-LOW", inline = "11")
inp_show_quarterly_low   = input.bool(false, title = "Low |",           group = "TIME OPEN-HIGH-LOW", inline = "11")
inp_quarterly_col        = input.color(color.orange, title = "",        group = "TIME OPEN-HIGH-LOW", inline = "11")
inp_quarterly_line_width = input.int(4, title = "", minval = 1,         group = "TIME OPEN-HIGH-LOW", inline = "11")
quarterly_ok = timeframe.isintraday or timeframe.isdaily or timeframe.isweekly
f_create_level("open", new_quarter, not new_quarter, global_open_array, inp_quarterly_col, inp_quarterly_line_width, inp_ext, open_line_style, text_size, "QO", 0, inp_show_quarterly_open and quarterly_ok)
f_create_level("high", new_quarter, not new_quarter, global_high_array, inp_quarterly_col, inp_quarterly_line_width, inp_ext, high_line_style, text_size, "QH", 0, inp_show_quarterly_high and quarterly_ok)
f_create_level("low", new_quarter, not new_quarter, global_low_array, inp_quarterly_col, inp_quarterly_line_width, inp_ext, low_line_style, text_size, "QL", 0, inp_show_quarterly_low and quarterly_ok)
inp_show_prev_quarterly_open  = input.bool(false, title = "Prev Quarterly |", group = "PREV TIME OPEN-HIGH-LOW", inline = "12")
inp_show_prev_quarterly_high  = input.bool(false, title = "High |",                group = "PREV TIME OPEN-HIGH-LOW", inline = "12")
inp_show_prev_quarterly_low   = input.bool(false, title = "Low |",                 group = "PREV TIME OPEN-HIGH-LOW", inline = "12")
inp_prev_quarterly_col        = input.color(color.yellow, title = "",              group = "PREV TIME OPEN-HIGH-LOW", inline = "12")
inp_prev_quarterly_line_width = input.int(4, title = "", minval = 1,               group = "PREV TIME OPEN-HIGH-LOW", inline = "12")
f_create_level("open", new_quarter, not new_quarter, global_open_array, inp_prev_quarterly_col, inp_prev_quarterly_line_width, inp_ext, open_line_style, text_size, "PQO", 1, inp_show_prev_quarterly_open and quarterly_ok)
f_create_level("high", new_quarter, not new_quarter, global_high_array, inp_prev_quarterly_col, inp_prev_quarterly_line_width, inp_ext, high_line_style, text_size, "PQH", 1, inp_show_prev_quarterly_high and quarterly_ok)
f_create_level("low", new_quarter, not new_quarter, global_low_array, inp_prev_quarterly_col, inp_prev_quarterly_line_width, inp_ext, low_line_style, text_size, "PQL", 1, inp_show_prev_quarterly_low and quarterly_ok)
inp_show_yearly_open        = input.bool(true,  title = "Yearly      |", group = "TIME OPEN-HIGH-LOW", inline = "13")
inp_show_yearly_high        =  input.bool(false, title = "High |",        group = "TIME OPEN-HIGH-LOW", inline = "13")
inp_show_yearly_low         =  input.bool(false, title = "Low |",         group = "TIME OPEN-HIGH-LOW", inline = "13")
inp_yearly_col =        input.color(color.red, title = "",                group = "TIME OPEN-HIGH-LOW", inline = "13")
inp_yearly_line_width = input.int(5, title = "", minval = 1,              group = "TIME OPEN-HIGH-LOW", inline = "13")
yearly_ok = timeframe.isintraday or timeframe.isdaily or timeframe.isweekly or (timeframe.ismonthly and timeframe.multiplier < 12)
f_create_level("open", new_year, not new_year, global_open_array, inp_yearly_col, inp_yearly_line_width, inp_ext, open_line_style, text_size, "YO", 0, inp_show_yearly_open and yearly_ok)
f_create_level("high", new_year, not new_year, global_high_array, inp_yearly_col, inp_yearly_line_width, inp_ext, high_line_style, text_size, "YH", 0, inp_show_yearly_high and yearly_ok)
f_create_level("low", new_year, not new_year, global_low_array, inp_yearly_col, inp_yearly_line_width, inp_ext, low_line_style, text_size, "YL", 0, inp_show_yearly_low and yearly_ok)
inp_show_prev_yearly_open   =  input.bool(false,  title = "Prev Yearly   |", group = "PREV TIME OPEN-HIGH-LOW", inline = "14")
inp_show_prev_yearly_high   =  input.bool(false, title = "High |",              group = "PREV TIME OPEN-HIGH-LOW", inline = "14")
inp_show_prev_yearly_low    =  input.bool(false, title = "Low |",               group = "PREV TIME OPEN-HIGH-LOW", inline = "14")
inp_prev_yearly_col =        input.color(color.maroon, title = "",              group = "PREV TIME OPEN-HIGH-LOW", inline = "14")
inp_prev_yearly_line_width = input.int(5, title = "", minval = 1,               group = "PREV TIME OPEN-HIGH-LOW", inline = "14")
f_create_level("open", new_year, not new_year, global_open_array, inp_prev_yearly_col, inp_prev_yearly_line_width, inp_ext, open_line_style, text_size, "PYO", 1, inp_show_prev_yearly_open and yearly_ok)
f_create_level("high", new_year, not new_year, global_high_array, inp_prev_yearly_col, inp_prev_yearly_line_width, inp_ext, high_line_style, text_size, "PYH", 1, inp_show_prev_yearly_high and yearly_ok)
f_create_level("low", new_year, not new_year, global_low_array, inp_prev_yearly_col, inp_prev_yearly_line_width, inp_ext, low_line_style, text_size, "PYL", 1, inp_show_prev_yearly_low and yearly_ok)
inp_show_monday_high        =  input.bool(true,  title = "High    |",       group = "MONDAY", inline = "14")
inp_show_monday_low         =  input.bool(true,  title = "Low |",        group = "MONDAY", inline = "14")
inp_monday_col =        input.color(color.fuchsia, title = "", group = "MONDAY", inline = "14")
inp_monday_line_width = input.int(1, title = "", minval = 1, group = "MONDAY", inline = "14")
monday_ok = timeframe.isintraday
f_create_level("high", new_week, is_monday, global_high_array, inp_monday_col, inp_monday_line_width, inp_ext, open_line_style, text_size, "Monday HIGH", 0, inp_show_monday_high and monday_ok)
f_create_level("low", new_week, is_monday, global_low_array, inp_monday_col, inp_monday_line_width, inp_ext, open_line_style, text_size, "Monday LOW", 0, inp_show_monday_low and monday_ok)
inp_show_prev_monday_high   =  input.bool(false, title = "Prev High |",       group = "MONDAY", inline = "15")
inp_show_prev_monday_low    =  input.bool(false, title = "Low |",        group = "MONDAY", inline = "15")
inp_prev_monday_col =        input.color(color.purple, title = "", group = "MONDAY", inline = "15")
inp_prev_monday_line_width = input.int(1, title = "", minval = 1, group = "MONDAY", inline = "15")
f_create_level("high", new_week, is_monday, global_high_array, inp_prev_monday_col, inp_prev_monday_line_width, inp_ext, open_line_style, text_size, "PMonday HIGH", 1, inp_show_prev_monday_high and monday_ok)
f_create_level("low", new_week, is_monday, global_low_array, inp_prev_monday_col, inp_prev_monday_line_width, inp_ext, open_line_style, text_size, "PMonday LOW", 1, inp_show_prev_monday_low and monday_ok)
// }

shownum = input(defval=true, title='Show Divergence Number', group='DIVERGENCE FOR MANY')
showlast = input(defval=false, title='Show Only Last Divergence', group='DIVERGENCE FOR MANY')
dontconfirm = input(defval=false, title='Don\'t Wait for Confirmation', group='DIVERGENCE FOR MANY')
showlines = input(defval=false, title='Show Divergence Lines', group='DIVERGENCE FOR MANY')
showpivot = input(defval=false, title='Show Pivot Points', group='DIVERGENCE FOR MANY')
calcmacd = input(defval=true, title='MACD', group='DIVERGENCE FOR MANY')
calcmacda = input(defval=true, title='MACD Histogram', group='DIVERGENCE FOR MANY')
calcrsi = input(defval=true, title='RSI', group='DIVERGENCE FOR MANY')
calcstoc = input(defval=true, title='Stochastic', group='DIVERGENCE FOR MANY')
calccci = input(defval=true, title='CCI', group='DIVERGENCE FOR MANY')
calcmom = input(defval=true, title='Momentum', group='DIVERGENCE FOR MANY')
calcobv = input(defval=true, title='OBV', group='DIVERGENCE FOR MANY')
calcvwmacd = input(true, title='VWmacd', group='DIVERGENCE FOR MANY')
calccmf = input(true, title='Chaikin Money Flow', group='DIVERGENCE FOR MANY')
calcmfi = input(true, title='Money Flow Index', group='DIVERGENCE FOR MANY')
calcext = input(true, title='Check External Indicator', group='DIVERGENCE FOR MANY')
prds = input.int(defval=5, title='Pivot Period', minval=1, maxval=50, group='DIVERGENCE FOR MANY')
sources = input.string(defval='Close', title='Source for Pivot Points', options=['Close', 'High/Low'], group='DIVERGENCE FOR MANY')
searchdiv = input.string(defval='Regular', title='Divergence Type', options=['Regular', 'Hidden', 'Regular/Hidden'], group='DIVERGENCE FOR MANY')
showindis = input.string(defval='Full', title='Show Indicator Names', options=['Full', 'First Letter', 'Don\'t Show'], group='DIVERGENCE FOR MANY')
showlimit = input.int(1, title='Minimum Number of Divergence', minval=1, maxval=11, group='DIVERGENCE FOR MANY')
maxpp = input.int(defval=10, title='Maximum Pivot Points to Check', minval=1, maxval=20, group='DIVERGENCE FOR MANY')
maxbars = input.int(defval=100, title='Maximum Bars to Check', minval=30, maxval=200, group='DIVERGENCE FOR MANY')
externalindi = input(defval=close, title='External Indicator', group='DIVERGENCE FOR MANY')
pos_reg_div_col = input(defval=color.yellow, title='Positive Regular Divergence', group='DIVERGENCE FOR MANY')
neg_reg_div_col = input(defval=color.navy, title='Negative Regular Divergence', group='DIVERGENCE FOR MANY')
pos_hid_div_col = input(defval=color.lime, title='Positive Hidden Divergence', group='DIVERGENCE FOR MANY')
neg_hid_div_col = input(defval=color.red, title='Negative Hidden Divergence', group='DIVERGENCE FOR MANY')
pos_div_text_col = input(defval=color.black, title='Positive Divergence Text Color', group='DIVERGENCE FOR MANY')
neg_div_text_col = input(defval=color.white, title='Negative Divergence Text Color', group='DIVERGENCE FOR MANY')
reg_div_l_style_ = input.string(defval='Solid', title='Regular Divergence Line Style', options=['Solid', 'Dashed', 'Dotted'], group='DIVERGENCE FOR MANY')
hid_div_l_style_ = input.string(defval='Dashed', title='Hdden Divergence Line Style', options=['Solid', 'Dashed', 'Dotted'], group='DIVERGENCE FOR MANY')
reg_div_l_width = input.int(defval=2, title='Regular Divergence Line Width', minval=1, maxval=5, group='DIVERGENCE FOR MANY')
hid_div_l_width = input.int(defval=1, title='Hidden Divergence Line Width', minval=1, maxval=5, group='DIVERGENCE FOR MANY')
cma1col = input.color(defval=color.lime, title='', inline='ma12', group='DIVERGENCE FOR MANY')
cma2col = input.color(defval=color.red, title='', inline='ma12', group='DIVERGENCE FOR MANY')
plot(showmas ? ta.sma(close, 50) : na, color=showmas ? cma1col : na)
plot(showmas ? ta.sma(close, 200) : na, color=showmas ? cma2col : na)
var reg_div_l_style = reg_div_l_style_ == 'Solid' ? line.style_solid : reg_div_l_style_ == 'Dashed' ? line.style_dashed : line.style_dotted
var hid_div_l_style = hid_div_l_style_ == 'Solid' ? line.style_solid : hid_div_l_style_ == 'Dashed' ? line.style_dashed : line.style_dotted
rsi = ta.rsi(close, 14)  // RSI
[macd, signal, deltamacd] = ta.macd(close, 12, 26, 9)  // MACD
moment = ta.mom(close, 10)  // Momentum
cci = ta.cci(close, 10)  // CCI
Obv = ta.obv  // OBV
stk = ta.sma(ta.stoch(close, high, low, 14), 3)  // Stoch
maFast = ta.vwma(close, 12)  // volume weighted macd
maSlow = ta.vwma(close, 26)
vwmacd = maFast - maSlow
Cmfm = (close - low - (high - close)) / (high - low)  // Chaikin money flow
Cmfv = Cmfm * volume
cmf = ta.sma(Cmfv, 21) / ta.sma(volume, 21)
Mfi = ta.mfi(close, 14)  // Moneyt Flow Index
var indicators_name = array.new_string(11)
var div_colors = array.new_color(4)
if Manyind and barstate.isfirst
    array.set(indicators_name, 0, showindis == 'Full' ? 'MACD' : 'M')
    array.set(indicators_name, 1, showindis == 'Full' ? 'Hist' : 'H')
    array.set(indicators_name, 2, showindis == 'Full' ? 'RSI' : 'E')
    array.set(indicators_name, 3, showindis == 'Full' ? 'Stoch' : 'S')
    array.set(indicators_name, 4, showindis == 'Full' ? 'CCI' : 'C')
    array.set(indicators_name, 5, showindis == 'Full' ? 'MOM' : 'M')
    array.set(indicators_name, 6, showindis == 'Full' ? 'OBV' : 'O')
    array.set(indicators_name, 7, showindis == 'Full' ? 'VWMACD' : 'V')
    array.set(indicators_name, 8, showindis == 'Full' ? 'CMF' : 'C')
    array.set(indicators_name, 9, showindis == 'Full' ? 'MFI' : 'M')
    array.set(indicators_name, 10, showindis == 'Full' ? 'Extrn' : 'X')
    array.set(div_colors, 0, pos_reg_div_col)
    array.set(div_colors, 1, neg_reg_div_col)
    array.set(div_colors, 2, pos_hid_div_col)
    array.set(div_colors, 3, neg_hid_div_col)
float phhhh = ta.pivothigh(sources == 'Close' ? close : high, prds, prds)
float pllll = ta.pivotlow(sources == 'Close' ? close : low, prds, prds)
plotshape(phhhh and showpivot, text='H', style=shape.labeldown, color=color.new(color.white, 100), textcolor=color.new(color.red, 0), location=location.abovebar, offset=-prds)
plotshape(pllll and showpivot, text='L', style=shape.labelup, color=color.new(color.white, 100), textcolor=color.new(color.lime, 0), location=location.belowbar, offset=-prds)
var int maxarraysize = 20
var ph_positions = array.new_int(maxarraysize, 0)
var pl_positions = array.new_int(maxarraysize, 0)
var ph_vals = array.new_float(maxarraysize, 0.)
var pl_vals = array.new_float(maxarraysize, 0.)
if Manyind and phhhh
    array.unshift(ph_positions, bar_index)
    array.unshift(ph_vals, phhhh)
    if array.size(ph_positions) > maxarraysize
        array.pop(ph_positions)
        array.pop(ph_vals)
if Manyind and pllll
    array.unshift(pl_positions, bar_index)
    array.unshift(pl_vals, pllll)
    if array.size(pl_positions) > maxarraysize
        array.pop(pl_positions)
        array.pop(pl_vals)
positive_regular_positive_hidden_divergence(src, cond) =>
    divlen = 0
    prsc = sources == 'Close' ? close : low
    if Manyind and dontconfirm or src > src[1] or close > close[1]
        startpoint = dontconfirm ? 0 : 1  // don't check last candle
        for x = 0 to maxpp - 1 by 1
            len = bar_index - array.get(pl_positions, x) + prds
            if array.get(pl_positions, x) == 0 or len > maxbars
                break
            if len > 5 and (cond == 1 and src[startpoint] > src[len] and prsc[startpoint] < nz(array.get(pl_vals, x)) or cond == 2 and src[startpoint] < src[len] and prsc[startpoint] > nz(array.get(pl_vals, x)))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - close[len]) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1 by 1
                    if src[y] < virtual_line1 or nz(close[y]) < virtual_line2
                        arrived := false
                        break
                    virtual_line1 -= slope1
                    virtual_line2 -= slope2
                    virtual_line2
                if arrived
                    divlen := len
                    break
    divlen
negative_regular_negative_hidden_divergence(src, cond) =>
    divlen = 0
    prsc = sources == 'Close' ? close : high
    if Manyind and dontconfirm or src < src[1] or close < close[1]
        startpoint = dontconfirm ? 0 : 1  // don't check last candle
        for x = 0 to maxpp - 1 by 1
            len = bar_index - array.get(ph_positions, x) + prds
            if array.get(ph_positions, x) == 0 or len > maxbars
                break
            if len > 5 and (cond == 1 and src[startpoint] < src[len] and prsc[startpoint] > nz(array.get(ph_vals, x)) or cond == 2 and src[startpoint] > src[len] and prsc[startpoint] < nz(array.get(ph_vals, x)))
                slope1 = (src[startpoint] - src[len]) / (len - startpoint)
                virtual_line1 = src[startpoint] - slope1
                slope2 = (close[startpoint] - nz(close[len])) / (len - startpoint)
                virtual_line2 = close[startpoint] - slope2
                arrived = true
                for y = 1 + startpoint to len - 1 by 1
                    if src[y] > virtual_line1 or nz(close[y]) > virtual_line2
                        arrived := false
                        break
                    virtual_line1 -= slope1
                    virtual_line2 -= slope2
                    virtual_line2
                if arrived
                    divlen := len
                    break
    divlen
calculate_divs(cond, indicator_1) =>
    divs = array.new_int(4, 0)
    array.set(divs, 0, cond and (searchdiv == 'Regular' or searchdiv == 'Regular/Hidden') ? positive_regular_positive_hidden_divergence(indicator_1, 1) : 0)
    array.set(divs, 1, cond and (searchdiv == 'Regular' or searchdiv == 'Regular/Hidden') ? negative_regular_negative_hidden_divergence(indicator_1, 1) : 0)
    array.set(divs, 2, cond and (searchdiv == 'Hidden' or searchdiv == 'Regular/Hidden') ? positive_regular_positive_hidden_divergence(indicator_1, 2) : 0)
    array.set(divs, 3, cond and (searchdiv == 'Hidden' or searchdiv == 'Regular/Hidden') ? negative_regular_negative_hidden_divergence(indicator_1, 2) : 0)
    divs
var all_divergences = array.new_int(44)  // 11 indicators * 4 divergence = 44 elements
array_set_divs(div_pointer, index) =>
    for x = 0 to 3 by 1
        array.set(all_divergences, index * 4 + x, array.get(div_pointer, x))
array_set_divs(calculate_divs(calcmacd, macd), 0)
array_set_divs(calculate_divs(calcmacda, deltamacd), 1)
array_set_divs(calculate_divs(calcrsi, rsi), 2)
array_set_divs(calculate_divs(calcstoc, stk), 3)
array_set_divs(calculate_divs(calccci, cci), 4)
array_set_divs(calculate_divs(calcmom, moment), 5)
array_set_divs(calculate_divs(calcobv, Obv), 6)
array_set_divs(calculate_divs(calcvwmacd, vwmacd), 7)
array_set_divs(calculate_divs(calccmf, cmf), 8)
array_set_divs(calculate_divs(calcmfi, Mfi), 9)
array_set_divs(calculate_divs(calcext, externalindi), 10)
total_div = 0
for x = 0 to array.size(all_divergences) - 1 by 1
    total_div += math.round(math.sign(array.get(all_divergences, x)))
    total_div
if Manyind and total_div < showlimit
    array.fill(all_divergences, 0)
var pos_div_lines = array.new_line(0)
var neg_div_lines = array.new_line(0)
var pos_div_labels = array.new_label(0)
var neg_div_labels = array.new_label(0)
delete_old_pos_div_lines() =>
    if array.size(pos_div_lines) > 0
        for j = 0 to array.size(pos_div_lines) - 1 by 1
            line.delete(array.get(pos_div_lines, j))
        array.clear(pos_div_lines)
delete_old_neg_div_lines() =>
    if array.size(neg_div_lines) > 0
        for j = 0 to array.size(neg_div_lines) - 1 by 1
            line.delete(array.get(neg_div_lines, j))
        array.clear(neg_div_lines)
delete_old_pos_div_labels() =>
    if array.size(pos_div_labels) > 0
        for j = 0 to array.size(pos_div_labels) - 1 by 1
            label.delete(array.get(pos_div_labels, j))
        array.clear(pos_div_labels)
delete_old_neg_div_labels() =>
    if array.size(neg_div_labels) > 0
        for j = 0 to array.size(neg_div_labels) - 1 by 1
            label.delete(array.get(neg_div_labels, j))
        array.clear(neg_div_labels)
delete_last_pos_div_lines_label(n) =>
    if n > 0 and array.size(pos_div_lines) >= n
        asz = array.size(pos_div_lines)
        for j = 1 to n by 1
            line.delete(array.get(pos_div_lines, asz - j))
            array.pop(pos_div_lines)
        if array.size(pos_div_labels) > 0
            label.delete(array.get(pos_div_labels, array.size(pos_div_labels) - 1))
            array.pop(pos_div_labels)
delete_last_neg_div_lines_label(n) =>
    if n > 0 and array.size(neg_div_lines) >= n
        asz = array.size(neg_div_lines)
        for j = 1 to n by 1
            line.delete(array.get(neg_div_lines, asz - j))
            array.pop(neg_div_lines)
        if array.size(neg_div_labels) > 0
            label.delete(array.get(neg_div_labels, array.size(neg_div_labels) - 1))
            array.pop(neg_div_labels)
pos_reg_div_detected = false
neg_reg_div_detected = false
pos_hid_div_detected = false
neg_hid_div_detected = false
var last_pos_div_lines = 0
var last_neg_div_lines = 0
var remove_last_pos_divs = false
var remove_last_neg_divs = false
if Manyind and pllll
    remove_last_pos_divs := false
    last_pos_div_lines := 0
    last_pos_div_lines
if Manyind and phhhh
    remove_last_neg_divs := false
    last_neg_div_lines := 0
    last_neg_div_lines
divergence_text_top = ''
divergence_text_bottom = ''
distances = array.new_int(0)
dnumdiv_top = 0
dnumdiv_bottom = 0
top_label_col = color.white
bottom_label_col = color.white
old_pos_divs_can_be_removed = true
old_neg_divs_can_be_removed = true
startpoint = dontconfirm ? 0 : 1  // used for don't confirm option
for x = 0 to 10 by 1
    div_type = -1
    for y = 0 to 3 by 1
        if array.get(all_divergences, x * 4 + y) > 0  // any divergence?
            div_type := y
            if y % 2 == 1
                dnumdiv_top += 1
                top_label_col := array.get(div_colors, y)
                top_label_col
            if y % 2 == 0
                dnumdiv_bottom += 1
                bottom_label_col := array.get(div_colors, y)
                bottom_label_col
            if not array.includes(distances, array.get(all_divergences, x * 4 + y))  // line not exist ?
                array.push(distances, array.get(all_divergences, x * 4 + y))
                new_line = showlines ? line.new(x1=bar_index - array.get(all_divergences, x * 4 + y), y1=sources == 'Close' ? close[array.get(all_divergences, x * 4 + y)] : y % 2 == 0 ? low[array.get(all_divergences, x * 4 + y)] : high[array.get(all_divergences, x * 4 + y)], x2=bar_index - startpoint, y2=sources == 'Close' ? close[startpoint] : y % 2 == 0 ? low[startpoint] : high[startpoint], color=array.get(div_colors, y), style=y < 2 ? reg_div_l_style : hid_div_l_style, width=y < 2 ? reg_div_l_width : hid_div_l_width) : na
                if y % 2 == 0
                    if old_pos_divs_can_be_removed
                        old_pos_divs_can_be_removed := false
                        if not showlast and remove_last_pos_divs
                            delete_last_pos_div_lines_label(last_pos_div_lines)
                            last_pos_div_lines := 0
                            last_pos_div_lines
                        if showlast
                            delete_old_pos_div_lines()
                    array.push(pos_div_lines, new_line)
                    last_pos_div_lines += 1
                    remove_last_pos_divs := true
                    remove_last_pos_divs
                if y % 2 == 1
                    if old_neg_divs_can_be_removed
                        old_neg_divs_can_be_removed := false
                        if not showlast and remove_last_neg_divs
                            delete_last_neg_div_lines_label(last_neg_div_lines)
                            last_neg_div_lines := 0
                            last_neg_div_lines
                        if showlast
                            delete_old_neg_div_lines()
                    array.push(neg_div_lines, new_line)
                    last_neg_div_lines += 1
                    remove_last_neg_divs := true
                    remove_last_neg_divs
            if y == 0
                pos_reg_div_detected := true
                pos_reg_div_detected
            if y == 1
                neg_reg_div_detected := true
                neg_reg_div_detected
            if y == 2
                pos_hid_div_detected := true
                pos_hid_div_detected
            if y == 3
                neg_hid_div_detected := true
                neg_hid_div_detected
    if div_type >= 0
        divergence_text_top += (div_type % 2 == 1 ? showindis != 'Don\'t Show' ? array.get(indicators_name, x) + '\n' : '' : '')
        divergence_text_bottom += (div_type % 2 == 0 ? showindis != 'Don\'t Show' ? array.get(indicators_name, x) + '\n' : '' : '')
        divergence_text_bottom
if Manyind and showindis != 'Don\'t Show' or shownum
    if shownum and dnumdiv_top > 0
        divergence_text_top += str.tostring(dnumdiv_top)
        divergence_text_top
    if shownum and dnumdiv_bottom > 0
        divergence_text_bottom += str.tostring(dnumdiv_bottom)
        divergence_text_bottom
    if divergence_text_top != ''
        if showlast
            delete_old_neg_div_labels()
        array.push(neg_div_labels, label.new(x=bar_index, y=math.max(high, high[1]), text=divergence_text_top, color=top_label_col, textcolor=neg_div_text_col, style=label.style_label_down))
    if divergence_text_bottom != ''
        if showlast
            delete_old_pos_div_labels()
        array.push(pos_div_labels, label.new(x=bar_index, y=math.min(low, low[1]), text=divergence_text_bottom, color=bottom_label_col, textcolor=pos_div_text_col, style=label.style_label_up))
alertcondition(pos_reg_div_detected, title='Positive Regular Divergence Detected', message='Positive Regular Divergence Detected')
alertcondition(neg_reg_div_detected, title='Negative Regular Divergence Detected', message='Negative Regular Divergence Detected')
alertcondition(pos_hid_div_detected, title='Positive Hidden Divergence Detected', message='Positive Hidden Divergence Detected')
alertcondition(neg_hid_div_detected, title='Negative Hidden Divergence Detected', message='Negative Hidden Divergence Detected')
alertcondition(pos_reg_div_detected or pos_hid_div_detected, title='Positive Divergence Detected', message='Positive Divergence Detected')
alertcondition(neg_reg_div_detected or neg_hid_div_detected, title='Negative Divergence Detected', message='Negative Divergence Detected')
// }

// VWAP {
hideonDWM = input(false, title="Hide VWAP on 1D or Above", group="VWAP Settings")
var anchor = input.string(defval = "Session", title="Anchor Period",
 options=["Session", "Week", "Month", "Quarter", "Year", "Decade", "Century", "Earnings", "Dividends", "Splits"], group="VWAP Settings")
src = input(title = "Source", defval = hlc3, group="VWAP Settings")
offset = input(0, title="Offset", group="VWAP Settings")
showBand_1 = input(true, title="", group="Standard Deviation Bands Settings", inline="band_1")
stdevMult_1 = input(1.0, title="Bands Multiplier #1", group="Standard Deviation Bands Settings", inline="band_1")
showBand_2 = input(false, title="", group="Standard Deviation Bands Settings", inline="band_2")
stdevMult_2 = input(2.0, title="Bands Multiplier #2", group="Standard Deviation Bands Settings", inline="band_2")
showBand_3 = input(false, title="", group="Standard Deviation Bands Settings", inline="band_3")
stdevMult_3 = input(3.0, title="Bands Multiplier #3", group="Standard Deviation Bands Settings", inline="band_3")

if vwaponoff and barstate.islast and ta.cum(volume) == 0
    runtime.error("No volume is provided by the data vendor.")

new_earnings = request.earnings(syminfo.tickerid, earnings.actual, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_dividends = request.dividends(syminfo.tickerid, dividends.gross, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)
new_split = request.splits(syminfo.tickerid, splits.denominator, barmerge.gaps_on, barmerge.lookahead_on, ignore_invalid_symbol=true)

isNewPeriod = switch anchor
	"Earnings"  => not na(new_earnings)
	"Dividends" => not na(new_dividends)
	"Splits"    => not na(new_split)
	"Session"   => timeframe.change("D")
	"Week"      => timeframe.change("W")
	"Month"     => timeframe.change("M")
	"Quarter"   => timeframe.change("3M")
	"Year"      => timeframe.change("12M")
	"Decade"    => timeframe.change("12M") and year % 10 == 0
	"Century"   => timeframe.change("12M") and year % 100 == 0
	=> false

isEsdAnchor = anchor == "Earnings" or anchor == "Dividends" or anchor == "Splits"
if vwaponoff and na(src[1]) and not isEsdAnchor
	isNewPeriod := true

float vwapValue = na
float upperBandValue1 = na
float lowerBandValue1 = na
float upperBandValue2 = na
float lowerBandValue2 = na
float upperBandValue3 = na
float lowerBandValue3 = na

if vwaponoff and not (hideonDWM and timeframe.isdwm)
    [_vwap, _stdevUpper, _] = ta.vwap(src, isNewPeriod, 1)
	vwapValue := _vwap
    stdevAbs = _stdevUpper - _vwap
	upperBandValue1 := _vwap + stdevAbs * stdevMult_1
	lowerBandValue1 := _vwap - stdevAbs * stdevMult_1
	upperBandValue2 := _vwap + stdevAbs * stdevMult_2
	lowerBandValue2 := _vwap - stdevAbs * stdevMult_2
	upperBandValue3 := _vwap + stdevAbs * stdevMult_3
	lowerBandValue3 := _vwap - stdevAbs * stdevMult_3

plot(vwapValue, title="VWAP", color=#2962FF, offset=offset)
// }

// HH/HL/LH/LL {
lefthh  = input.int(5, minval=1, title='Pivot Length left',       group='HH, LL, LH, HL', inline='1')
righthh = input.int(5, minval=1, title='  |  Pivot Length right', group='HH, LL, LH, HL', inline='1')
pvtLenL = lefthh
pvtLenR = righthh
pvthi_ = ta.pivothigh(high, pvtLenL, pvtLenR)
pvtlo_ = ta.pivotlow(low, pvtLenL, pvtLenR)
pvthi = pvthi_
pvtlo = pvtlo_
valuewhen_1 = ta.valuewhen(pvthi, high[pvtLenR], 1)
valuewhen_2 = ta.valuewhen(pvthi, high[pvtLenR], 0)
higherhigh = na(pvthi) ? na : valuewhen_1 < valuewhen_2 ? pvthi : na
valuewhen_3 = ta.valuewhen(pvthi, high[pvtLenR], 1)
valuewhen_4 = ta.valuewhen(pvthi, high[pvtLenR], 0)
lowerhigh = na(pvthi) ? na : valuewhen_3 > valuewhen_4 ? pvthi : na
valuewhen_5 = ta.valuewhen(pvtlo, low[pvtLenR], 1)
valuewhen_6 = ta.valuewhen(pvtlo, low[pvtLenR], 0)
higherlow = na(pvtlo) ? na : valuewhen_5 < valuewhen_6 ? pvtlo : na
valuewhen_7 = ta.valuewhen(pvtlo, low[pvtLenR], 1)
valuewhen_8 = ta.valuewhen(pvtlo, low[pvtLenR], 0)
lowerlow = na(pvtlo) ? na : valuewhen_7 > valuewhen_8 ? pvtlo : na
plotshape(ShowHHLL ? higherhigh : na, title='HH', style=shape.triangledown, location=location.abovebar, color=color.new(color.green, 100),  text='HH', textcolor=color.new(color.green, 0), offset=-pvtLenR)
plotshape(ShowHHLL ? higherlow  : na, title='HL', style=shape.triangleup,   location=location.belowbar, color=color.new(color.green, 100),  text='HL', textcolor=color.new(color.green, 0), offset=-pvtLenR)
plotshape(ShowHHLL ? lowerhigh  : na, title='LH', style=shape.triangledown, location=location.abovebar, color=color.new(color.red, 100),    text='LH', textcolor=color.new(color.red, 0),   offset=-pvtLenR)
plotshape(ShowHHLL ? lowerlow   : na, title='LL', style=shape.triangleup,   location=location.belowbar, color=color.new(color.red, 100),    text='LL', textcolor=color.new(color.red, 0),   offset=-pvtLenR)
counthi = 0
countlo = 0
counthi := na(pvthi) ? nz(counthi[1]) + 1 : 0
countlo := na(pvtlo) ? nz(countlo[1]) + 1 : 0
pvthis = 0.0
pvtlos = 0.0
pvthis := na(pvthi) ? pvthis[1] : high[pvtLenR]
pvtlos := na(pvtlo) ? pvtlos[1] : low[pvtLenR]
hipc = pvthis != pvthis[1] ? na : color.new(color.red, 50)
lopc = pvtlos != pvtlos[1] ? na : color.new(color.green, 50)
// }
//////////////////////////// Displacement /////////////////////////////////////////
//Indikatör Ayarları
showDisplacement = input(false, title="Show Displacement [TFO]", group="Displacement [TFO] Settings")
require_fvg = input.bool(true, "Require FVG", group="Displacement [TFO] Settings")
disp_type = input.string("Open to Close", "Displacement Type", options=['Open to Close', 'High to Low'], group="Displacement [TFO] Settings")
std_len = input.int(100, minval=1, title="Displacement Length", tooltip="How far back the script will look to determine the candle range standard deviation", group="Displacement [TFO] Settings")
std_x = input.int(4, minval=0, title="Displacement Strength", group="Displacement [TFO] Settings")
disp_color = input.color(color.yellow, "Bar Color", group="Displacement [TFO] Settings")
///////////////////////////////
///////////////////////////////
// Displacement Hesaplaması ve Gösterimi///////////////////////////////
// Displacement Hesaplaması
var bool displacement = na
if showDisplacement
    candle_range = disp_type == "Open to Close" ? math.abs(open - close) : high - low
    std = ta.stdev(candle_range, std_len) * std_x
    fvg = close[1] > open[1] ? high[2] < low[0] : low[2] > high[0]
    displacement := require_fvg ? candle_range[1] > std[1] and fvg : candle_range > std

// Bar Renkleme (if bloğunun dışında)
barcolor(displacement ? disp_color : na, offset = require_fvg ? -1 : na)
////////////////////////////////////////////////////////////////////
// POI SUPPLY & DEMAND{
//POISETTINGS
//

//      POI INDICATOR SETTINGS
swing_length = input.int(10, title = 'Swing High/Low Length', group = 'Settings', minval = 1, maxval = 50)
history_of_demand_to_keep = input.int(20, title = 'History To Keep', minval = 5, maxval = 50)
box_width = input.float(2.5, title = 'Supply/Demand Box Width', group = 'Settings', minval = 1, maxval = 10, step = 0.5)

//      INDICATOR VISUAL SETTINGS
show_zigzag = input.bool(false, title = 'Show Zig Zag', group = 'Visual Settings', inline = '1')
show_price_action_labels = input.bool(false, title = 'Show Price Action Labels', group = 'Visual Settings', inline = '2')

supply_color = input.color(color.new(#9c27b0,75), title = 'Supply', group = 'Visual Settings', inline = '3')
supply_outline_color = input.color(color.new(#f23645,75), title = 'Outline', group = 'Visual Settings', inline = '3')

demand_color = input.color(color.rgb(15, 211, 214, 70), title = 'Demand', group = 'Visual Settings', inline = '4')
demand_outline_color = input.color(color.new(#4caf50,75), title = 'Outline', group = 'Visual Settings', inline = '4')

bos_label_color = input.color(color.white, title = 'BOS Label', group = 'Visual Settings', inline = '5')
poi_label_color = input.color(color.white, title = 'POI Label', group = 'Visual Settings', inline = '7')

swing_type_color = input.color(color.black, title = 'Price Action Label', group = 'Visual Settings', inline = '8')
zigzag_color = input.color(color.new(#000000,0), title = 'Zig Zag', group = 'Visual Settings', inline = '9')

//
//END SETTINGS
//


//
//FUNCTIONS
//

//      FUNCTION TO ADD NEW AND REMOVE LAST IN ARRAY
f_array_add_pop(array, new_value_to_add) =>
    array.unshift(array, new_value_to_add)
    array.pop(array)

//      FUNCTION SWING H & L LABELS
f_sh_sl_labels(array, swing_type) =>

    var string label_text = na
    if swing_type == 1
        if array.get(array, 0) >= array.get(array, 1)
            label_text := 'HH'
        else
            label_text := 'LH'
        label.new(bar_index - swing_length, array.get(array,0), text = label_text, style=label.style_label_down, textcolor = swing_type_color, color = color.new(swing_type_color, 100), size = size.tiny)
    
    else if swing_type == -1
        if array.get(array, 0) >= array.get(array, 1)
            label_text := 'HL'
        else
            label_text := 'LL'
        label.new(bar_index - swing_length, array.get(array,0), text = label_text, style=label.style_label_up, textcolor = swing_type_color, color = color.new(swing_type_color, 100), size = size.tiny)

//      FUNCTION MAKE SURE SUPPLY ISNT OVERLAPPING
f_check_overlapping(new_poi, box_array, atr) =>

    atr_threshold = atr * 2
    okay_to_draw = true

    for i = 0 to array.size(box_array) - 1
        top = box.get_top(array.get(box_array, i))
        bottom = box.get_bottom(array.get(box_array, i))
        poi = (top + bottom) / 2

        upper_boundary = poi + atr_threshold
        lower_boundary = poi - atr_threshold

        if new_poi >= lower_boundary and new_poi <= upper_boundary
            okay_to_draw := false
            break
        else 
            okay_to_draw := true
    okay_to_draw


//      FUNCTION TO DRAW SUPPLY OR DEMAND ZONE
f_supply_demand(value_array, bn_array, box_array, label_array, box_type, atr) =>
    if(Poi)
        atr_buffer = atr * (box_width / 10)
        box_left = array.get(bn_array, 0)
        box_right = bar_index

        var float box_top = 0.00
        var float box_bottom = 0.00
        var float poi = 0.00


        if box_type == 1
            box_top := array.get(value_array, 0)
            box_bottom := box_top - atr_buffer
            poi := (box_top + box_bottom) / 2
        else if box_type == -1
            box_bottom := array.get(value_array, 0)
            box_top := box_bottom + atr_buffer
            poi := (box_top + box_bottom) / 2

        okay_to_draw = f_check_overlapping(poi, box_array, atr)
        // okay_to_draw = true

        //delete oldest box, and then create a new box and add it to the array
        if box_type == 1 and okay_to_draw
            box.delete( array.get(box_array, array.size(box_array) - 1) )
            f_array_add_pop(box_array, box.new( left = box_left, top = box_top, right = box_right, bottom = box_bottom, border_color = supply_outline_color,bgcolor = supply_color, extend = extend.right, text = 'SUPPLY', text_halign = text.align_center, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))
            
            box.delete( array.get(label_array, array.size(label_array) - 1) )
            f_array_add_pop(label_array, box.new( left = box_left, top = poi, right = box_right, bottom = poi, border_color = color.new(poi_label_color,90),bgcolor = color.new(poi_label_color,90), extend = extend.right, text = 'POI', text_halign = text.align_left, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))

        else if box_type == -1 and okay_to_draw
            box.delete( array.get(box_array, array.size(box_array) - 1) )
            f_array_add_pop(box_array, box.new( left = box_left, top = box_top, right = box_right, bottom = box_bottom, border_color = demand_outline_color,bgcolor = demand_color, extend = extend.right,  text = 'DEMAND', text_halign = text.align_center, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))
            
            box.delete( array.get(label_array, array.size(label_array) - 1) )
            f_array_add_pop(label_array, box.new( left = box_left, top = poi, right = box_right, bottom = poi, border_color = color.new(poi_label_color,90),bgcolor = color.new(poi_label_color,90), extend = extend.right,  text = 'POI', text_halign = text.align_left, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))
//      FUNCTION TO CHANGE SUPPLY/DEMAND TO A BOS IF BROKEN
f_sd_to_bos(box_array, bos_array, label_array, zone_type) =>

    if zone_type == 1
        for i = 0 to array.size(box_array) - 1
            level_to_break = box.get_top(array.get(box_array,i))
            // if ta.crossover(close, level_to_break)
            if close >= level_to_break
                copied_box = box.copy(array.get(box_array,i))
                f_array_add_pop(bos_array, copied_box)
                mid = (box.get_top(array.get(box_array,i)) + box.get_bottom(array.get(box_array,i))) / 2
                box.set_top(array.get(bos_array,0), mid)
                box.set_bottom(array.get(bos_array,0), mid)
                box.set_extend( array.get(bos_array,0), extend.none)
                box.set_right( array.get(bos_array,0), bar_index)
                box.set_text( array.get(bos_array,0), 'BOS' )
                box.set_text_color( array.get(bos_array,0), bos_label_color)
                box.set_text_size( array.get(bos_array,0), size.small)
                box.set_text_halign( array.get(bos_array,0), text.align_center)
                box.set_text_valign( array.get(bos_array,0), text.align_center)
                box.delete(array.get(box_array, i))
                box.delete(array.get(label_array, i))


    if zone_type == -1
        for i = 0 to array.size(box_array) - 1
            level_to_break = box.get_bottom(array.get(box_array,i))
            // if ta.crossunder(close, level_to_break)
            if close <= level_to_break
                copied_box = box.copy(array.get(box_array,i))
                f_array_add_pop(bos_array, copied_box)
                mid = (box.get_top(array.get(box_array,i)) + box.get_bottom(array.get(box_array,i))) / 2
                box.set_top(array.get(bos_array,0), mid)
                box.set_bottom(array.get(bos_array,0), mid)
                box.set_extend( array.get(bos_array,0), extend.none)
                box.set_right( array.get(bos_array,0), bar_index)
                box.set_text( array.get(bos_array,0), 'BOS' )
                box.set_text_color( array.get(bos_array,0), bos_label_color)
                box.set_text_size( array.get(bos_array,0), size.small)
                box.set_text_halign( array.get(bos_array,0), text.align_center)
                box.set_text_valign( array.get(bos_array,0), text.align_center)
                box.delete(array.get(box_array, i))
                box.delete(array.get(label_array, i))



//      FUNCTION MANAGE CURRENT BOXES BY CHANGING ENDPOINT
f_extend_box_endpoint(box_array) =>

    for i = 0 to array.size(box_array) - 1
        box.set_right(array.get(box_array, i), bar_index + 100)


//
//END FUNCTIONS
//  


//
//CALCULATIONS
//

//      CALCULATE ATR 
atr = ta.atr(50)

//      CALCULATE SWING HIGHS & SWING LOWS
swing_high = ta.pivothigh(high, swing_length, swing_length)
swing_low = ta.pivotlow(low, swing_length, swing_length)

//      ARRAYS FOR SWING H/L & BN 
var swing_high_values = array.new_float(5,0.00)
var swing_low_values = array.new_float(5,0.00)

var swing_high_bns = array.new_int(5,0)
var swing_low_bns = array.new_int(5,0)

//      ARRAYS FOR SUPPLY / DEMAND
var current_supply_box = array.new_box(history_of_demand_to_keep, na)
var current_demand_box = array.new_box(history_of_demand_to_keep, na)

//      ARRAYS FOR SUPPLY / DEMAND POI LABELS
var current_supply_poi = array.new_box(history_of_demand_to_keep, na)
var current_demand_poi = array.new_box(history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos = array.new_box(5, na)
var demand_bos = array.new_box(5, na)
//
//END CALCULATIONS
//

//      NEW SWING HIGH
if not na(swing_high)

    //MANAGE SWING HIGH VALUES
    f_array_add_pop(swing_high_values, swing_high)
    f_array_add_pop(swing_high_bns, bar_index[swing_length])
    if show_price_action_labels
        f_sh_sl_labels(swing_high_values, 1)

    f_supply_demand(swing_high_values, swing_high_bns, current_supply_box, current_supply_poi, 1, atr)

//      NEW SWING LOW
else if not na(swing_low)

    //MANAGE SWING LOW VALUES
    f_array_add_pop(swing_low_values, swing_low)
    f_array_add_pop(swing_low_bns, bar_index[swing_length])
    if show_price_action_labels
        f_sh_sl_labels(swing_low_values, -1)
    
    f_supply_demand(swing_low_values, swing_low_bns, current_demand_box, current_demand_poi, -1, atr)


f_sd_to_bos(current_supply_box, supply_bos, current_supply_poi, 1)
f_sd_to_bos(current_demand_box, demand_bos, current_demand_poi, -1)

f_extend_box_endpoint(current_supply_box)
f_extend_box_endpoint(current_demand_box)

//ZIG ZAG
h = ta.highest(high, swing_length * 2 + 1)
l = ta.lowest(low, swing_length * 2 + 1)
f_isMin(len) =>
    l == low[len]
f_isMax(len) =>
    h == high[len]

var dirUp = false
var lastLow = high * 100
var lastHigh = 0.0
var timeLow = bar_index
var timeHigh = bar_index
var line li = na

f_drawLine() =>
    _li_color = show_zigzag ? zigzag_color : color.new(#ffffff,100)
    line.new(timeHigh - swing_length, lastHigh, timeLow - swing_length, lastLow, xloc.bar_index, color=_li_color, width=2)

if dirUp
    if f_isMin(swing_length) and low[swing_length] < lastLow
        lastLow := low[swing_length]
        timeLow := bar_index
        line.delete(li)
        li := f_drawLine()
        li

    if f_isMax(swing_length) and high[swing_length] > lastLow
        lastHigh := high[swing_length]
        timeHigh := bar_index
        dirUp := false
        li := f_drawLine()
        li

if not dirUp
    if f_isMax(swing_length) and high[swing_length] > lastHigh
        lastHigh := high[swing_length]
        timeHigh := bar_index
        line.delete(li)
        li := f_drawLine()
        li
    if f_isMin(swing_length) and low[swing_length] < lastHigh
        lastLow := low[swing_length]
        timeLow := bar_index
        dirUp := true
        li := f_drawLine()
        if f_isMax(swing_length) and high[swing_length] > lastLow
            lastHigh := high[swing_length]
            timeHigh := bar_index
            dirUp := false
            li := f_drawLine()
            li

// if barstate.islast
    // label.new(x = bar_index + 10, y = close[1], text = str.tostring( array.size(current_supply_poi) ))
//     label.new(x = bar_index + 20, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 0))))
//     label.new(x = bar_index + 30, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 1))))
//     label.new(x = bar_index + 40, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 2))))
//     label.new(x = bar_index + 50, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 3))))
//     label.new(x = bar_index + 60, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 4))))

// Draws lines for each of up to 500 pivots that have never been revisited at the present moment in time.

tooltipD  = "Delays deletion by nBars or nTime of any Pivot lines that are touched or crossed by realtime bars formed after indicator has been loaded "  +
 "on chart.\n\nWhen using nBars the bar duration is from next bar until close of Nth bar. This is the recommended delay method.\n\nWhen using nTime "    + 
 "units the time duration is the minimum time of delay desired, the actual time delay may exceed specified amount. Note that this delay method can be "  +
 "computationally quite expensive and may cause erratic behavior when used with a large delay, large number of lines, or when the indicator has been "   +
 "loaded for a long while.\n\nElapsed time is checked upon each price or volume update. Frequency of the updates is dependent upon liquidity. If time "  + 
 "delay is set to 3 seconds and the next bar update after touch does not occur until 5 seconds later then the delay would be 5 seconds since that is "   + 
 "when the next check for elapsed time occurs."
tooltipQ  = "Extend levels nBars to the right of current bar. Show number of newest Pivot levels to keep"

inBarsL_  = input.int   (defval = 2,                       title = 'Pivot Bars Left',    minval = 0, maxval = 100,                   group = 'Likidite Ayarları', inline = '0')
inBars_R  = input.int   (defval = 2,                       title = ' Right',             minval = 0, maxval = 100,                   group = 'Likidite Ayarları', inline = '0')
inStyles  = input.string(defval = 'Dotted',                title = 'Lines Show As',     options = ['Dashed', 'Dotted', 'Solid'],     group = 'Likidite Ayarları', inline = '1')
inWidths  = input.int   (defval = 1,                       title = ' Wide ',                                                         group = 'Likidite Ayarları', inline = '1')
inDelayN  = input.int   (defval = 0,                       title = 'Delay Removal',      minval = 0,               step = 1,         group = 'Likidite Ayarları', inline = '2')
inDelayT  = input.string(defval = 'nBars',                 title = ' Units', options = ['nBars', 'nSecs', 'nMins', 'nHrs', 'nDays'], group = 'Likidite Ayarları', inline = '2', tooltip = tooltipD)
inOffset  = input.int   (defval = 7,                       title = 'Extend Right    ',   minval = 0, maxval = 100, step = 1,         group = 'Likidite Ayarları', inline = '3')
inQueueN  = input.int   (defval = 50,                      title = ' Show ',             minval = 0, maxval = 500, step = 1,         group = 'Likidite Ayarları', inline = '3', tooltip = tooltipQ)
inColrHi  = input.color (defval = color.new(#ff9800, 0), title = 'Colors Used   Hi',                                               group = 'Likidite Ayarları', inline = '4')
inColrLo  = input.color (defval = color.new(#00e676, 0), title = 'Lo',                                                             group = 'Likidite Ayarları', inline = '4')
inColrHL  = input.color (defval = color.new(#9c27b0, 0), title = 'HL',                                                             group = 'Likidite Ayarları', inline = '4')
inLabels  = input       (defval = true,                    title = 'Show Price Labels',                                              group = 'Likidite Ayarları')
inPlaced  = input       (defval = false,                   title = 'On Right',                                                       group = 'Likidite Ayarları', inline = '5')
inColors  = input       (defval = false,                   title = 'Show Pivot Bars',                                                group = 'Likidite Ayarları', inline = '6')

drawLabl(_offset, _y, _colr, _styl)=> label.new(bar_index + _offset, _y, str.tostring(_y, format.mintick), xloc.bar_index,
 yloc.price, color(na), not _styl ? label.style_none : label.style_label_left, _colr, size.normal, text.align_left)

drawLine(_inset, _offset, _y, _colr, _styl, _wide)=> line.new(bar_index - _inset, _y, bar_index + _offset, _y, xloc.bar_index,
 extend.none, _colr, _styl == 'Dashed' ? line.style_dashed : _styl == 'Dotted' ? line.style_dotted : line.style_solid, _wide)

pivotsHi = ta.pivothigh(high, inBarsL_, inBars_R)                          // hi pivot occurs
pivotsLo = ta.pivotlow(  low, inBarsL_, inBars_R)                          // lo pivot occurs
sourceHi = high[inBars_R]                                                  // price value of pivot when hi pivot is confirmed 
sourceLo =  low[inBars_R]                                                  // price value of pivot when lo pivot is confirmed
isColrHi = inLabels ? inColrHi : na                                        // disable labels with na or use transparent color(na)
isColrLo = inLabels ? inColrLo : na                                        // disable labels with na or use transparent color(na)
delayAmt =
 inDelayT == 'nBars' ? inDelayN + 1     :
 inDelayT == 'nSecs' ? inDelayN * 1e3   :
 inDelayT == 'nMins' ? inDelayN * 6e4 :
 inDelayT == 'nHrs'  ? inDelayN * 3.6e6 : 2.52e7                           // added + 1 to nBars to extend to the open of the Nth + 1 bar
                                                                           // when delay type is nTime convert nSecs to nMilliseconds

type pivType                                                               // declare UD type
    string Type                                                            // declare field for string value of 'h' or 'l'
    float  Valu                                                            // declare field for source value of pivot
    int    BarI                                                            // declare field for bar_index when touched
    int    Time                                                            // declare field for bar time when touched
    bool   Mark                                                            // declare field for flagging touched lines
    label  Labl                                                            // declare field for label of source value
    line   Line                                                            // declare field for line of source value

var pivArray = array.new<pivType>()                                        // declare an array of UD type to hold UDT objects 

if  pivotsHi and showPivot                                                               // if condition occurs
    piv = pivType.new('h', sourceHi, na, na, false,
     drawLabl(inOffset, sourceHi, isColrHi, inPlaced),
     drawLine(inBars_R, inOffset, sourceHi, inColrHi, inStyles, inWidths)) // create an object of UDT type
    array.push(pivArray, piv)                                              // populate array with element object

if  pivotsLo and showPivot                                                               // if condition occurs
    piv = pivType.new('l', sourceLo, na, na, false,
     drawLabl(inOffset, sourceLo, isColrLo, inPlaced),
     drawLine(inBars_R, inOffset, sourceLo, inColrLo, inStyles, inWidths)) // create an object of UDT type
    array.push(pivArray, piv)                                              // populate array with element object  

snapShot = timenow                                                         // create a single timestamp for loop, less accurate but more efficient
for i = (array.size(pivArray) == 0 ?
 na : array.size(pivArray) - 1) to 0 by 1                                  // loop backwards through array of objects
    obj = array.get(pivArray, i)                                           // assign the array's element i to arbitrarily named 'obj'
    breaksHi = obj.Type == 'h' and high >= obj.Valu                        // check if pivot hi and touched by high
    breaksLo = obj.Type == 'l' and  low <= obj.Valu                        // check if pivot lo and touched by low
    if obj.Mark == false and (breaksHi or breaksLo)                        // check unflagged (untouched) element objects for source touches
        obj.BarI := bar_index                                              // set element object's BarI field for later evaluating elapsed bars
        obj.Time := timenow                                                // set element object's Time field for later evaluating elapsed time
        obj.Mark := true                                                   // set element object's flag field for later removal once delay has been met
        trgtValu  = str.tostring(obj.Valu, '#.00')                         // format string of pivot level for alert
        srceValu  = str.tostring(close, '#.00')                            // format string of current price for alert
        if breaksHi
            alert(syminfo.prefix + ' ' + syminfo.ticker + ' ' + 
             timeframe.period +
             '\nPrice Touched Pivot Hi\nPrice: ' +
             srceValu + '\nPivot: ' + trgtValu,
             alert.freq_once_per_bar)                                      // alert when pivot hi is touched or broken by high
        if breaksLo
            alert(syminfo.prefix + ' ' + syminfo.ticker + ' ' +
             timeframe.period +
             '\nPrice Touched Pivot Lo\nPivot: ' +
             trgtValu + '\nPrice: ' + srceValu,
             alert.freq_once_per_bar)                                      // alert when pivot lo is touched or broken by low
    if obj.Mark == true and (barstate.ishistory or inDelayN == 0 or
     (inDelayT == 'nBars' and bar_index - obj.BarI >= delayAmt) or
     (inDelayT != 'nBars' and snapShot  - obj.Time >= delayAmt))           // check if flagged touches have met required amount of delay
        label.delete(obj.Labl)                                             // remove stored label drawing from chart
        line.delete(obj.Line)                                              // remove stored line drawing from chart
        array.remove(pivArray, i)                                          // remove touched element object
    label.set_x(obj.Labl, bar_index + inOffset)                            // offset remaining labels for current bar realignment
    line.set_x2(obj.Line, bar_index + inOffset)                            // offset remaining lines for current bar realignment

trashBin = array.size(pivArray) - inQueueN                                 // get current number of items exceeding queue
if trashBin > 0                                                            // if number of elements exceeds queue                   
    for elmnt  = trashBin - 1 to 0 by 1                                    // loop backwards through elements to be trashed 
        getEl  = array.get(pivArray, elmnt)                                // get object element from array
        label.delete(getEl.Labl)                                           // delete drawing before removal of object element
        line.delete(getEl.Line)                                            // delete drawing before removal of object element
        array.remove(pivArray, elmnt)                                      // remove object element from items array

barColor = pivotsHi and pivotsLo ? inColrHL :
 pivotsHi ? inColrHi : pivotsLo ? inColrLo : na                            // assign appropriate color for type of fractal
barcolor(inColors and pivotsHi ? barColor : na, -inBars_R)                 // colorize hi pivot bars
barcolor(inColors and pivotsLo ? barColor : na, -inBars_R)                 // colorize lo pivot bars

// Volume Profiles

//==========================
//Inputs
//==========================
sessionType = input.string('Daily', 'Session Type', options=['Tokyo','London','New York','Daily','Weekly', 'Monthly', 'Quarterly', 'Yearly'])

showVP = input.bool(true, 'Show Volume Profile, VAH,POC,VAL', group = 'Global Settings')
showProf = input.bool(true, 'Show Volume Profile', group='Volume Display')
showPoc = input.bool(true, 'Show POC', group='Volume Display')
showVA = input.bool(true, 'Show VAH and VAL', group='Volume Display')
showVAb = input.bool(false, 'Show Value Area Box', group='Volume Display')
showCur = input.bool(true, 'Show Live Zone', group='Volume Display')
showLabels = input.bool(true, 'Show Session Lables', group='Volume Display')
showFx = input.bool(false, 'Show Forex Sessions (no profile)', group='Volume Display')
resolution = input.int(30, 'Resolution', minval=5, tooltip='The higher the value, the more refined of a profile, but less profiles shown on chart', group='Volume Display')
VAwid = input.int(70, 'Value Area Volume %', minval=1, maxval=100, group='Volume Display')
dispMode = input.string('Mode 2', 'Bar Mode', ['Mode 1', 'Mode 2', 'Mode 3'], group='Volume Display')
volType = input.string('Volume', 'Profile Data Type', options=['Volume', 'Open Interest'], group='Volume Display')
smoothVol = input.bool(false, 'Smooth Volume Data', tooltip='Useful for assets that have very large spikes in volume over large bars - helps create better profiles', group='Volume Display')
dataTf = ''

bullCol = input.color(color.rgb(76, 175, 79, 50), 'Up Volume', group='Appearance')
bearCol = input.color(color.rgb(255, 82, 82, 50), 'Down Volume', group='Appearance')
VAbCol = input.color(color.rgb(107, 159, 255, 90), 'Value Area Box', group='Appearance' )
pocCol = input.color(color.red, 'POC', inline='p', group='Appearance')
pocWid = input.int(1, 'Thickness', inline='p', group='Appearance')
vahCol = input.color(color.aqua, 'VAH', inline='h', group='Appearance')
vahWid = input.int(1, 'Thickness', inline='h', group='Appearance')
valCol = input.color(color.aqua, 'VAL', inline='l', group='Appearance')
valWid = input.int(1, 'Thickness', inline='l', group='Appearance')
boxBg = input.color(color.rgb(255, 153, 0, 100), 'Box', inline='m', group='Appearance')
boxWid = input.int(1, 'Thickness', inline='m', group='Appearance')

//==========================
//Constants / Variable Declaration
//========================== 
var int zoneStart = 0
var int tokyoStart = 0
var int londonStart = 0
var int nyStart = 0
int lookback = bar_index - zoneStart
var activeZone = false

// Defining arrays that store the information
var vpGreen = array.new_float(resolution, 0) // Sum of volume on long bars
var vpRed = array.new_float(resolution, 0) // Same thing but with red bars
var zoneBounds = array.new_float(resolution, 0) // array that stores the highest value that can be in a zone

//Values to store current intra bar data
var float[] ltfOpen =  array.new_float(0)
var float[] ltfClose =  array.new_float(0)
var float[] ltfHigh =  array.new_float(0)
var float[] ltfLow =  array.new_float(0)
var float[] ltfVolume = array.new_float(0)

//Getting OI Data
string userSymbol = syminfo.prefix + ":" + syminfo.ticker
string openInterestTicker = str.format("{0}_OI", userSymbol)
string timeframe = syminfo.type == "futures" and timeframe.isintraday ? "1D" : timeframe.period
deltaOi = request.security(openInterestTicker, timeframe, close-close[1], ignore_invalid_symbol = true)

//Selecting what vol type to use
vol() =>
    out = smoothVol ? ta.ema(volume, 5) : volume
    if volType == 'Open Interest'
        out := deltaOi
    out

//Getting intrabar intial data
[dO, dC, dH, dL, dV] = request.security_lower_tf(syminfo.tickerid, dataTf, [open, close, high, low, vol()])

//==========================
//Functions
//==========================
resetProfile(enable) =>
    if enable
        array.fill(vpGreen, 0)
        array.fill(vpRed, 0)
        array.clear(ltfOpen)
        array.clear(ltfHigh)
        array.clear(ltfLow)
        array.clear(ltfClose)
        array.clear(ltfVolume)

profHigh = ta.highest(high, lookback+1)[1]
profLow = ta.lowest(low, lookback+1)[1]

tr = ta.atr(1)

get_vol(y11, y12, y21, y22, height, vol) =>
    nz(math.max(math.min(math.max(y11, y12), math.max(y21, y22)) - math.max(math.min(y11, y12), math.min(y21, y22)), 0) * vol / height)

profileAdd(o, h, l, c, v, g, w) =>
    //Array to store how much to distribute in each zone, on scale of 1 for full gap size to 0
    zoneDist = array.new_float(resolution, 0)
    distSum = 0.0
    // Going over each zone
    for i = 0 to array.size(vpGreen) - 1
        // Checking to see if cur bar is in zone
        zoneTop = array.get(zoneBounds, i)
        zoneBot = zoneTop - g

        body_top = math.max(c, o)
        body_bot = math.min(c, o)
        itsgreen = c >= o

        topwick = h - body_top
        bottomwick = body_bot - l
        body = body_top - body_bot

        bodyvol = body * v / (2 * topwick + 2 * bottomwick + body)
        topwickvol = 2 * topwick * v / (2 * topwick + 2 * bottomwick + body)
        bottomwickvol = 2 * bottomwick * v / (2 * topwick + 2 * bottomwick + body)

        if volType == 'Volume'
            array.set(vpGreen, i, array.get(vpGreen, i) + (itsgreen ? get_vol(zoneBot, zoneTop, body_bot, body_top, body, bodyvol) : 0) + get_vol(zoneBot, zoneTop, body_top, h, topwick, topwickvol) / 2 + get_vol(zoneBot, zoneTop, body_bot, l, bottomwick, bottomwickvol) / 2)
            array.set(vpRed, i, array.get(vpRed, i) + (itsgreen ? 0 : get_vol(zoneBot, zoneTop, body_bot, body_top, body, bodyvol)) + get_vol(zoneBot, zoneTop, body_top, h, topwick, topwickvol) / 2 + get_vol(zoneBot, zoneTop, body_bot, l, bottomwick, bottomwickvol) / 2)
        else if volType == 'Open Interest'
            if v > 0    
                array.set(vpGreen, i, array.get(vpGreen, i) + get_vol(zoneBot, zoneTop, body_bot, body_top, body, v))// + get_vol(zoneBot, zoneTop, body_top, h, topwick, topwickvol) / 2 + get_vol(zoneBot, zoneTop, body_bot, l, bottomwick, bottomwickvol) / 2)
            if v < 0
                array.set(vpRed, i, array.get(vpRed, i) + get_vol(zoneBot, zoneTop, body_bot, body_top, body, -v))// + get_vol(zoneBot, zoneTop, body_top, h, topwick, topwickvol) / 2 + get_vol(zoneBot, zoneTop, body_bot, l, bottomwick, bottomwickvol) / 2)

calcSession(update) =>
    array.fill(vpGreen, 0)
    array.fill(vpRed, 0)
    if bar_index > lookback and update
        gap = (profHigh - profLow) / resolution

        // Defining profile bounds
        for i = 0 to resolution - 1
            array.set(zoneBounds, i, profHigh - gap * i)

        // Putting each bar inside zone into the volume profile array
        if array.size(ltfOpen) > 0
            for j = 0 to array.size(ltfOpen) - 1    
                profileAdd(array.get(ltfOpen, j), array.get(ltfHigh, j), array.get(ltfLow, j), array.get(ltfClose, j), array.get(ltfVolume, j), gap, 1)

pocLevel() =>
    float maxVol = 0
    int levelInd = 0
    for i = 0 to array.size(vpRed) - 1
        if array.get(vpRed, i) + array.get(vpGreen, i) > maxVol
            maxVol := array.get(vpRed, i) + array.get(vpGreen, i)
            levelInd := i
    
    float outLevel = na
    if levelInd != array.size(vpRed) - 1
        outLevel := array.get(zoneBounds, levelInd) - (array.get(zoneBounds, levelInd) - array.get(zoneBounds, levelInd+1)) / 2
    outLevel

valueLevels(poc) =>
    float gap = (profHigh - profLow) / resolution
    float volSum = array.sum(vpRed) + array.sum(vpGreen)
    float volCnt = 0
    
    float vah = profHigh
    float val = profLow

    //Finding poc index
    int pocInd = 0
    for i = 0 to array.size(zoneBounds)-2
        if array.get(zoneBounds, i) >= poc and array.get(zoneBounds, i + 1) < poc
            pocInd := i
    
    volCnt += (array.get(vpRed, pocInd) + array.get(vpGreen, pocInd))
    for i = 1 to array.size(vpRed)
        if pocInd + i >= 0 and pocInd + i < array.size(vpRed)    
            volCnt += (array.get(vpRed, pocInd + i) + array.get(vpGreen, pocInd + i))
            if volCnt >= volSum * (VAwid/100)    
                break 
            else
                val := array.get(zoneBounds, pocInd + i) - gap
        if pocInd - i >= 0 and pocInd - i < array.size(vpRed)    
            volCnt += (array.get(vpRed, pocInd - i) + array.get(vpGreen, pocInd - i))
            if volCnt >= volSum * (VAwid/100)    
                break 
            else
                vah := array.get(zoneBounds, pocInd - i)

    [val, vah]

drawNewZone(update) =>
    if bar_index > lookback and update and array.sum(vpGreen) + array.sum(vpRed) > 0
        gap = (profHigh - profLow) / resolution
        float leftMax = bar_index[lookback]
        float rightMax = bar_index[int(lookback / 1.4)]
        float rightMaxVol = array.max(vpGreen)+array.max(vpRed)
        float buffer = gap / 10
        if showLabels
            label.new((bar_index - 1 + int(leftMax))/2, profHigh, sessionType, color=color.rgb(0,0,0,100), textcolor=chart.fg_color)
        if showProf and showVP
            for i = 0 to array.size(vpRed) - 1
                greenEnd = int(leftMax + (rightMax - leftMax) * (array.get(vpGreen, i) / rightMaxVol))
                redEnd = int(greenEnd + (rightMax - leftMax) * (array.get(vpRed, i) / rightMaxVol))
                if dispMode == 'Mode 2'
                    box.new(int(leftMax), array.get(zoneBounds, i) - buffer, greenEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bullCol, border_width=0)
                    box.new(greenEnd, array.get(zoneBounds, i) - buffer, redEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bearCol, border_width=0)
                else if dispMode == 'Mode 1'
                    box.new(int(leftMax), array.get(zoneBounds, i) - buffer, greenEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bullCol, border_width=0)
                else 
                    box.new(int(leftMax), array.get(zoneBounds, i) - buffer, greenEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bullCol, border_width=0)
                    box.new(int(leftMax)-redEnd+greenEnd, array.get(zoneBounds, i) - buffer, int(leftMax), array.get(zoneBounds, i) - gap + buffer, bgcolor=bearCol, border_width=0)

        box.new(int(leftMax), profHigh, bar_index-1, profLow, chart.fg_color, boxWid, line.style_dashed, bgcolor=boxBg)
        poc = pocLevel()
        [val, vah] = valueLevels(poc)
        if showPoc and showVP
            line.new(int(leftMax), poc, bar_index-1, poc, color=pocCol, width=pocWid)
        if showVA and showVP
            line.new(int(leftMax), vah, bar_index-1, vah, color=vahCol, width=vahWid)
            line.new(int(leftMax), val, bar_index-1, val, color=valCol, width=valWid)
        if showVAb and showVP
            box.new(int(leftMax), vah, bar_index-1, val, border_color=color.rgb(54, 58, 69, 100), bgcolor=VAbCol)


    //if update    
    //    resetProfile(true)

drawCurZone(update, delete) =>
    var line pocLine = na
    var line vahLine = na
    var line valLine = na
    var box outBox = na
    var label sessionLab = na

    var redBoxes = array.new_box(array.size(vpRed), na)
    var greenBoxes = array.new_box(array.size(vpRed), na)

    if bar_index > lookback and update and array.sum(vpGreen) + array.sum(vpRed) > 0
        //Clearing the previous boxes and array
        if not na(pocLine)
            line.delete(pocLine)
        if not na(vahLine)
            line.delete(vahLine)
        if not na(valLine)
            line.delete(valLine)
        if not na(outBox)
            box.delete(outBox)
        if not na(sessionLab)
            label.delete(sessionLab)

        for i = 0 to array.size(redBoxes) - 1
            if not na(array.get(redBoxes, i))
                box.delete(array.get(redBoxes, i))
                box.delete(array.get(greenBoxes, i))

        
        gap = (profHigh - profLow) / resolution
        float leftMax = bar_index[lookback]
        float rightMax = bar_index[int(lookback / 1.4)]
        float rightMaxVol = array.max(vpGreen)+array.max(vpRed)
        float buffer = gap / 10
        if showLabels
            sessionLab := label.new((bar_index - 1 + int(leftMax))/2, profHigh, sessionType, color=color.rgb(0,0,0,100), textcolor=chart.fg_color)
        if showProf and showVP
            for i = 0 to array.size(vpRed) - 1
                greenEnd = int(leftMax + (rightMax - leftMax) * (array.get(vpGreen, i) / rightMaxVol))
                redEnd = int(greenEnd + (rightMax - leftMax) * (array.get(vpRed, i) / rightMaxVol))
                if dispMode == 'Mode 2'
                    array.set(greenBoxes, i, box.new(int(leftMax), array.get(zoneBounds, i) - buffer, greenEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bullCol, border_width=0))
                    array.set(redBoxes, i, box.new(greenEnd, array.get(zoneBounds, i) - buffer, redEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bearCol, border_width=0))
                else if dispMode == 'Mode 1'
                    array.set(greenBoxes, i, box.new(int(leftMax), array.get(zoneBounds, i) - buffer, greenEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bullCol, border_width=0))
                else 
                    array.set(greenBoxes, i, box.new(int(leftMax), array.get(zoneBounds, i) - buffer, greenEnd, array.get(zoneBounds, i) - gap + buffer, bgcolor=bullCol, border_width=0))
                    array.set(redBoxes, i, box.new(int(leftMax)-redEnd+greenEnd, array.get(zoneBounds, i) - buffer, int(leftMax), array.get(zoneBounds, i) - gap + buffer, bgcolor=bearCol, border_width=0))
        
        outBox := box.new(int(leftMax), profHigh, bar_index-1, profLow, chart.fg_color, boxWid, line.style_dashed, bgcolor=boxBg)
        

        poc = pocLevel()
        [val, vah] = valueLevels(poc)
        if showPoc and showVP    
            line.delete(pocLine)
            pocLine := line.new(int(leftMax), poc, bar_index-1, poc, color=pocCol, width=pocWid)
        if showVA and showVP
            line.delete(vahLine)
            line.delete(valLine)            
            vahLine := line.new(int(leftMax), vah, bar_index-1, vah, color=vahCol, width=vahWid)
            valLine := line.new(int(leftMax), val, bar_index-1, val, color=valCol, width=valWid)
        if showVAb and showVP
            box.new(int(leftMax), vah, bar_index-1, val, border_color=color.rgb(54, 58, 69, 100), bgcolor=VAbCol)

    if delete
        box.delete(outBox)
        line.delete(pocLine)
        line.delete(vahLine)
        line.delete(valLine)
        for i = 0 to array.size(greenBoxes)-1
            box.delete(array.get(greenBoxes, i))
        for i = 0 to array.size(redBoxes)-1
            box.delete(array.get(redBoxes, i))


drawForexBox(startBar, title, top, bottom) =>
    box.new(int(startBar), top, bar_index-1, bottom, chart.fg_color, boxWid, line.style_dashed, bgcolor=boxBg)
    if showLabels    
        label.new((bar_index - 1 + int(startBar))/2, top, title, color=color.rgb(0,0,0,100), textcolor=chart.fg_color)
    

combArray(arr1, arr2) =>
    out = array.copy(arr1)
    if array.size(arr2) > 0
        for i = 0 to array.size(arr2) - 1
            array.push(out, array.get(arr2, i))
    out

updateIntra(o, h, l, c, v) =>
    if array.size(o) > 0
        for i = 0 to array.size(o) - 1
            array.push(ltfOpen, array.get(o, i))
            array.push(ltfHigh,array.get(h, i)) 
            array.push(ltfLow,array.get(l, i)) 
            array.push(ltfClose,array.get(c, i)) 
            array.push(ltfVolume,array.get(v, i))
    

//==========================
//Calculations
//==========================
//Detecting different start dates
newDaily = dayofweek != dayofweek[1]
newWeekly = weekofyear != weekofyear[1]
newMonthly = (dayofmonth != dayofmonth[1] + 1) and (dayofmonth != dayofmonth[1])
newYearly = year != year[1]
newQuarterly = month != month[1] and (month - 1) % 3 == 0

utcHour = hour(time(timeframe.period, '0000-2400', 'GMT'), 'GMT')

newTokyo = utcHour != utcHour[1] + 1 and utcHour != utcHour[1]
endTokyo = utcHour >= 9 and utcHour[1] < 9

newLondon = utcHour >= 7 and utcHour[1] < 7
endLondon = utcHour >= 16 and utcHour[1] < 16

newNewYork = utcHour >= 13 and utcHour[1] < 13
endNewYork = utcHour >= 22 and utcHour[1] < 22

newSession = switch sessionType
    'Tokyo' => newTokyo
    'London' => newLondon
    'New York' => newNewYork
    'Daily' => newDaily
    'Weekly' => newWeekly
    'Monthly' => newMonthly
    'Yearly' => newYearly
    'Quarterly' => newQuarterly
    => newDaily

zoneEnd = switch sessionType
    'Tokyo' => endTokyo
    'London' => endLondon
    'New York' => endNewYork
    'Daily' => newDaily
    'Weekly' => newWeekly
    'Monthly' => newMonthly
    'Yearly' => newYearly
    'Quarterly' => newQuarterly
    => newDaily

isForex = showFx

//Re calculating and drawing zones
calcSession(zoneEnd or (barstate.islast and showCur and showVP))
drawNewZone(zoneEnd and showVP)
drawCurZone(barstate.islast and not zoneEnd and showCur and activeZone, zoneEnd and showVP)

//Reseting profie at start of new zone
resetProfile(newSession)

//Updating data arrays
updateIntra(dO, dH, dL, dC, dV)

//Reseting zone start value
if zoneEnd 
    activeZone := false

if newSession
    zoneStart := bar_index
    activeZone := true

if newLondon
    londonStart := bar_index 
if newTokyo
    tokyoStart := bar_index 
if newNewYork
    nyStart := bar_index

londonHigh = ta.highest(high, bar_index-londonStart+1)
tokyoHigh = ta.highest(high, bar_index-tokyoStart+1)
nyHigh = ta.highest(high, bar_index-nyStart+1)

londonLow = ta.lowest(low, bar_index-londonStart+1)
tokyoLow = ta.lowest(low, bar_index-tokyoStart+1)
nyLow = ta.lowest(low, bar_index-nyStart+1)

if endLondon and isForex
    drawForexBox(londonStart, 'London', londonHigh, londonLow)
if endNewYork and isForex
    drawForexBox(nyStart, 'New York', nyHigh, nyLow)
if endTokyo and isForex
    drawForexBox(tokyoStart, 'Tokyo', tokyoHigh, tokyoLow)


if(Trend)
    shortPeriod = input(30, title="shortPeriod")
    longPeriod = input(100, title="longPeriod")

    if barstate.islast
        float lowest_y2 = 60000
        float lowest_x2 = 0
        
        float highest_y2 = 0
        float highest_x2 = 0
        
        
        for i = 1 to shortPeriod
            if low[i] < lowest_y2
                lowest_y2 := low[i]
                lowest_x2 := i
            if high[i] > highest_y2
                highest_y2 := high[i]
                highest_x2 := i
                
        float lowest_y1 = 60000
        float lowest_x1 = 0
        
        float highest_y1 = 0
        float highest_x1 = 0
        
        for j = shortPeriod + 1 to longPeriod
            if low[j] < lowest_y1
                lowest_y1 := low[j]
                lowest_x1 := j
            if high[j] > highest_y1
                highest_y1 := high[j]
                highest_x1 := j
                
        sup = line.new(x1=bar_index[lowest_x1], y1=lowest_y1, x2=bar_index[lowest_x2], y2=lowest_y2, extend=extend.right, width=2, color=color.green)
        res = line.new(x1=bar_index[highest_x1], y1=highest_y1, x2=bar_index[highest_x2], y2=highest_y2, extend=extend.right, width=2, color=color.red)
        line.delete(sup[1])
        line.delete(res[1])
        if ta.crossunder(close, line.get_price(sup, bar_index[0]))
            alert("break support", freq=alert.freq_once_per_bar_close)

        if ta.crossover(close, line.get_price(res, bar_index[0]))
            alert("break resistance", freq=alert.freq_once_per_bar_close)