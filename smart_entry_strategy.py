from typing import Dict, Any, Optional, Tuple, List
import numpy as np
import pandas as pd
from loguru import logger

class SmartEntryStrategy:
    """
    Fibonacci seviyelerini kullanarak akıllı giriş stratejileri sunan sınıf.
    Volatilite bazlı ATR hesaplaması ile giriş seviyelerini dinamik olarak ayarlar.
    """

    def __init__(self):
        """
        SmartEntryStrategy sınıfını başlatır.
        """
        logger.info("Akıllı Giriş Stratejisi modülü başlatıldı")

        # Volatilite seviyeleri için eşik değerleri
        self.volatility_thresholds = {
            "low": 0.015,     # %1.5'den düşük ATR - düşük volatilite
            "medium": 0.03,   # %1.5 - %3 arası ATR - orta volatilite
            "high": 0.05      # %3'ten yüksek ATR - yüksek volatilite
        }

        # === Merkezileştirilmiş Eşik Değerleri ===

        # Dinamik Geri Çekilme (Dynamic Fallback) için eşik değeri
        self.max_fvg_distance_pct = 0.02  # FVG girişi fiyattan en fazla %2 uzakta olabilir

        # FVG + Fibonacci akıllı seçim eşiği
        self.fvg_price_distance_threshold = 0.03  # %3 - FVG uzaksa fiyata yakın Fib seç

        # Pure Fibonacci maksimum mesafe eşiği
        self.max_fibonacci_distance_pct = 0.013  # %1.3 - düşük fiyatlı semboller için optimize

        # FVG kombinasyon ağırlıkları
        self.fvg_weight = 0.6      # FVG EQ ağırlığı (%60)
        self.fibonacci_weight = 0.4 # Fibonacci ağırlığı (%40)

        # FVG giriş pozisyonu (bull/bear için FVG bölgesi içindeki konum)
        self.fvg_entry_position = 0.3  # %30 - Bull için alt %30, Bear için üst %30

        # Stop Loss hesaplama eşikleri
        self.triple_pattern_sl_pct = 0.025  # %2.5 - TRIT/TRIB için sabit stop loss
        self.default_sl_pct = 0.025         # %2.5 - Varsayılan stop loss
        self.liquidity_buffer_pct = 0.01    # %1 - Likidite payı (MSB için)

        # Take Profit R:R oranları
        self.tp2_rr_ratio = 1.5  # TP2 için 1.5:1 R:R oranı
        self.tp3_rr_ratio = 2.0  # TP3 için 2:1 R:R oranı

    def _calculate_atr(self, candles: List[Dict[str, Any]], period: int = 14) -> Optional[float]:
        """
        ATR (Average True Range) hesaplar. Standart Wilder's Smoothing (EMA benzeri)
        yöntemini kullanarak daha hassas bir sonuç üretir.

        Args:
            candles: Mum verileri listesi (en yeni mum en sonda olacak şekilde)
            period: ATR periyodu (varsayılan: 14)

        Returns:
            float: Hesaplanan ATR yüzdesi veya None
        """
        if not candles or len(candles) < period + 1:
            logger.warning(f"ATR hesaplaması için yeterli mum verisi yok. En az {period+1} mum gerekli.")
            return None

        try:
            import pandas as pd

            # Mum verilerini pandas DataFrame'e çevir
            df = pd.DataFrame(candles)

            # Veri tiplerini düzelt
            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            df['close'] = pd.to_numeric(df['close'])

            # True Range (TR) hesaplaması
            high_low = df['high'] - df['low']
            high_prev_close = (df['high'] - df['close'].shift(1)).abs()
            low_prev_close = (df['low'] - df['close'].shift(1)).abs()

            # TR, bu üç değerin en büyüğüdür
            tr = pd.concat([high_low, high_prev_close, low_prev_close], axis=1).max(axis=1)

            # ATR hesaplaması (Wilder's Smoothing)
            # EMA'ya çok benzer ve standart yöntemdir. alpha = 1 / period
            atr = tr.ewm(alpha=1/period, adjust=False).mean().iloc[-1]

            # ATR'yi yüzde olarak ifade et (son kapanış fiyatına göre)
            last_price = df['close'].iloc[-1]
            atr_percentage = atr / last_price if last_price > 0 else 0

            logger.debug(f"Hassas ATR (EMA): {atr:.6f}, Yüzde: %{atr_percentage*100:.2f}")
            return atr_percentage

        except Exception as e:
            logger.error(f"ATR hesaplama hatası: {e}")
            return None

    def _get_volatility_level(self, atr_percentage: float) -> str:
        """
        ATR yüzdesine göre volatilite seviyesini belirler.

        Args:
            atr_percentage: ATR yüzdesi

        Returns:
            str: Volatilite seviyesi ('low', 'medium', 'high')
        """
        if atr_percentage < self.volatility_thresholds['low']:
            return 'low'
        elif atr_percentage < self.volatility_thresholds['medium']:
            return 'medium'
        else:
            return 'high'

    def calculate_entry_levels(self,
                              symbol: str,
                              stats: Dict[str, Any],
                              trade_direction: str,
                              fibonacci_data: Optional[Dict[str, Any]] = None,
                              order_blocks: Optional[Dict[str, Any]] = None,
                              swing_points: Optional[List[Dict[str, Any]]] = None,
                              pattern_name: Optional[str] = None,
                              candles: Optional[List[Dict[str, Any]]] = None,
                              fvg_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Fibonacci seviyelerini kullanarak akıllı giriş seviyeleri hesaplar.
        Volatilite bazlı ATR hesaplaması ile giriş seviyelerini dinamik olarak ayarlar.

        Impulsive hareketlerde FVG (Fair Value Gap) bölgelerini kullanarak
        daha etkili giriş noktaları belirler.

        Args:
            symbol: Kripto para sembolü
            stats: İstatistik verileri (son fiyat vb.)
            trade_direction: İşlem yönü ('bull' veya 'bear')
            fibonacci_data: Fibonacci analiz sonuçları (all_timeframe_data[symbol]['240']['fib_levels'])
            order_blocks: Order Block analiz sonuçları (kullanılmıyor)
            swing_points: Pivot noktaları (swing high/low) listesi (stop loss için)
            pattern_name: Tespit edilen pattern adı (TRIT, TRIB vb.)
            candles: Mum verileri listesi (ATR hesaplaması için)
            fvg_data: FVG (Fair Value Gap) analiz sonuçları (impulsive hareketler için)

        Returns:
            Dict: Hesaplanan giriş seviyeleri
        """
        # Varsayılan değerler
        entry_levels = {
            "primary_entry": None,  # Başarılı strateji bulunana kadar None kalacak
            "secondary_entry": None,
            "tertiary_entry": None,
            "stop_loss": None,
            "take_profit": None,
            "sl_percentage": None,
            "tp_percentage": None,
            "strategy_used": "none",  # Başarılı strateji bulunana kadar "none"
            "sl_strategy": None,  # Stop loss stratejisi (yapısal)
            "pattern_name": pattern_name  # Pattern adını sakla
        }

        # Son fiyatı al
        last_price = stats.get("last_price")
        if not last_price:
            logger.warning(f"[{symbol}] Giriş seviyeleri hesaplanamadı: Son fiyat bilgisi yok")
            return entry_levels

        # TRIT/TRIB patternleri için özel strateji: Direkt last_price giriş
        if pattern_name and ("TRIT" in pattern_name.upper() or "TRIB" in pattern_name.upper()):
            logger.info(f"[{symbol}] 🎯 {pattern_name} pattern tespit edildi. Özel strateji: last_price giriş, %2.5 stop loss.")

            # Basit volatilite hesaplama (sadece loglama için)
            atr_percentage = None
            volatility_level = "medium"
            if candles:
                atr_percentage = self._calculate_atr(candles)
                if atr_percentage is not None:
                    volatility_level = self._get_volatility_level(atr_percentage)

            entry_levels = {
                "primary_entry": last_price,
                "strategy_used": f"{pattern_name} Direct Entry",
                "volatility_level": volatility_level,
                "fibonacci_level": None,
                "fvg_distance": None,
                "pattern_name": pattern_name
            }

            if atr_percentage is not None:
                entry_levels["atr_percentage"] = atr_percentage

            logger.debug(f"[{symbol}] {pattern_name} pattern için diğer stratejiler atlandı.")
            return entry_levels

        # Volatilite seviyesini hesapla (eğer mum verileri mevcutsa)
        volatility_level = "unknown"
        atr_percentage = None

        if candles:
            atr_percentage = self._calculate_atr(candles)
            if atr_percentage is not None:
                volatility_level = self._get_volatility_level(atr_percentage)
                logger.debug(f"[{symbol}] Volatilite: {volatility_level.upper()} (ATR: %{atr_percentage*100:.2f})")
            else:
                logger.debug(f"[{symbol}] ATR hesaplanamadı.")
        else:
            logger.debug(f"[{symbol}] Mum verileri bulunamadı.")

        # FVG bazlı giriş stratejisi (impulsive hareketler için)
        fvg_entry_found = False
        if fvg_data and fibonacci_data:
            fvg_entry = self._calculate_fvg_fibonacci_entry(
                symbol,
                fvg_data,
                fibonacci_data,
                last_price,
                trade_direction,
                volatility_level,
                atr_percentage
            )

            if fvg_entry:
                # Dinamik Geri Çekilme (Dynamic Fallback) Kontrolü
                primary_fvg_entry = fvg_entry.get("primary_entry")
                distance_to_fvg_entry = abs(last_price - primary_fvg_entry) / last_price

                if distance_to_fvg_entry <= self.max_fvg_distance_pct:
                    # Mesafe kabul edilebilir, FVG stratejisini kullan
                    entry_levels.update(fvg_entry)
                    entry_levels["strategy_used"] = f"fvg_fibonacci (Volatilite: {volatility_level.upper()})"
                    logger.info(f"[{symbol}] ✅ FVG+Fib giriş (Yakın): {primary_fvg_entry:.4f} (Mesafe: %{distance_to_fvg_entry*100:.2f})")
                    fvg_entry_found = True
                else:
                    # Mesafe çok uzak, FVG stratejisini yok say ve standart Fibonacci'ye geç
                    logger.warning(f"[{symbol}] ⚠️ FVG+Fib girişi ({primary_fvg_entry:.4f}) çok uzak (%{distance_to_fvg_entry*100:.2f} > %{self.max_fvg_distance_pct*100:.1f}), standart Fib stratejisine geçiliyor.")
                    fvg_entry_found = False  # Bu satır çok önemli!

        # Normal Fibonacci giriş stratejisi (FVG bulunamazsa veya uygun değilse)
        if not fvg_entry_found and fibonacci_data is not None and isinstance(fibonacci_data, dict):
            fibonacci_entry = self._calculate_fibonacci_entry(
                symbol,
                fibonacci_data,
                last_price,
                trade_direction,
                volatility_level,
                atr_percentage
            )

            if fibonacci_entry:
                entry_levels.update(fibonacci_entry)
                entry_levels["strategy_used"] = f"fibonacci (Volatilite: {volatility_level.upper()})"
                logger.info(f"[{symbol}] ✅ Fibonacci giriş: {fibonacci_entry['primary_entry']:.4f}")
            else:
                logger.warning(f"[{symbol}] ❌ Fibonacci giriş hesaplanamadı.")
        elif not fvg_entry_found:
            logger.warning(f"[{symbol}] ❌ Fibonacci verileri bulunamadı.")


        # =============================================================================

        # Stop Loss ve Take Profit hesapla (sadece geçerli giriş varsa)
        if entry_levels["primary_entry"] is not None:
            sl_tp = self._calculate_sl_tp(
                entry_levels["primary_entry"],
                trade_direction,
                swing_points,
                pattern_name,
                fibonacci_data  # Fibonacci verilerini geçir
            )
            entry_levels.update(sl_tp)

            # Stop loss tipini ayrı anahtar olarak ekle (yapısal sonuç)
            if "sl_type" in sl_tp:
                entry_levels["sl_strategy"] = sl_tp["sl_type"]  # Yeni yapısal anahtar
                # Geriye uyumluluk için eski format da korunabilir (opsiyonel)
                # entry_levels["strategy_used"] += f" ({sl_tp['sl_type']} SL)"
        else:
            # Hiçbir strateji başarılı olmadı
            logger.warning(f"[{symbol}] ❌ Hiçbir giriş stratejisi başarılı olmadı. İşlem sinyali oluşturulmayacak.")

        return entry_levels

    def _calculate_fvg_fibonacci_entry(self,
                                      symbol: str,
                                      fvg_data: List[Dict[str, Any]],
                                      fibonacci_data: Dict[str, Any],
                                      last_price: float,
                                      trade_direction: str,
                                      volatility_level: str = "unknown",
                                      atr_percentage: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        FVG (Fair Value Gap) bölgeleri ile Fibonacci seviyelerini kombinleyerek
        impulsive hareketlerde daha etkili giriş seviyeleri hesaplar.

        Strateji:
        1. Fiyata en yakın, doldurulmamış FVG bölgesini bul
        2. Bu FVG bölgesine en yakın Fibonacci seviyesini tespit et
        3. FVG EQ seviyesi ile Fibonacci seviyesini kombine et
        4. Impulsive hareket sonrası ilk geri çekilmede giriş yap

        Args:
            symbol: Kripto para sembolü
            fvg_data: FVG analiz sonuçları listesi
            fibonacci_data: Fibonacci analiz sonuçları
            last_price: Son fiyat
            trade_direction: İşlem yönü ('bull' veya 'bear')
            volatility_level: Volatilite seviyesi
            atr_percentage: ATR yüzdesi

        Returns:
            Dict: FVG+Fibonacci kombinasyonu giriş seviyeleri veya None
        """
        if not fvg_data:
            logger.debug(f"[{symbol}] FVG verisi bulunamadı.")
            return None

        # Fibonacci seviyelerini al
        levels = fibonacci_data.get('levels', {})
        if not isinstance(levels, dict) or not levels:
            logger.debug(f"[{symbol}] Fibonacci seviyeleri bulunamadı.")
            return None

        # Seviyeleri float olarak al
        float_levels = {}
        for k, v in levels.items():
            try:
                float_key = float(k)
                float_levels[float_key] = v
            except Exception as ex:
                logger.error(f"[{symbol}] Fibonacci seviyesi anahtarı float'a çevrilemedi: {k} ({ex})")

        # Trade direction'a göre uygun FVG'leri filtrele
        suitable_fvgs = []
        for fvg in fvg_data:
            fvg_type = fvg.get('type')
            fvg_filled = fvg.get('filled', True)

            # Sadece doldurulmamış FVG'leri al
            if fvg_filled:
                continue

            # Trade direction ile uyumlu FVG'leri al
            if (trade_direction == "bull" and fvg_type == "bullish") or \
               (trade_direction == "bear" and fvg_type == "bearish"):

                fvg_eq = fvg.get('eq')
                fvg_top = fvg.get('top')
                fvg_bottom = fvg.get('bottom')

                if fvg_eq and fvg_top and fvg_bottom:
                    # Fiyata olan mesafeyi hesapla
                    distance_to_eq = abs(last_price - fvg_eq) / last_price

                    suitable_fvgs.append({
                        'fvg': fvg,
                        'eq': fvg_eq,
                        'top': fvg_top,
                        'bottom': fvg_bottom,
                        'distance_to_eq': distance_to_eq,
                        'type': fvg_type
                    })

        if not suitable_fvgs:
            logger.debug(f"[{symbol}] Uygun FVG bulunamadı (trade_direction: {trade_direction}).")
            return None

        # En yakın FVG'yi seç
        suitable_fvgs.sort(key=lambda x: x['distance_to_eq'])
        closest_fvg = suitable_fvgs[0]

        logger.debug(f"[{symbol}] En yakın {closest_fvg['type']} FVG: EQ={closest_fvg['eq']:.4f}")

        # Bu FVG'ye en yakın Fibonacci seviyesini bul
        fib_targets = []
        bull_levels = [0.382, 0.5, 0.618, 0.705, 0.786, 0.886]
        bear_levels = [0.114, 0.214, 0.295, 0.382, 0.5, 0.618]

        target_levels = bull_levels if trade_direction == "bull" else bear_levels

        for level in target_levels:
            if level in float_levels:
                level_price = float_levels[level]['price']
                # FVG EQ'ya olan mesafeyi hesapla
                distance_to_fvg = abs(closest_fvg['eq'] - level_price) / closest_fvg['eq']

                fib_targets.append({
                    "level": level,
                    "price": level_price,
                    "distance_to_fvg": distance_to_fvg,
                    "distance_to_current": abs(last_price - level_price) / last_price
                })

        if not fib_targets:
            logger.debug(f"[{symbol}] FVG için uygun Fibonacci seviyesi bulunamadı.")
            return None

        # Akıllı Fibonacci seviyesi seçimi: Fiyat konumunu dikkate al
        # Önce fiyata en yakın Fibonacci seviyesini bul
        fib_targets_by_price = sorted(fib_targets, key=lambda x: x['distance_to_current'])
        closest_fib_to_price = fib_targets_by_price[0] if fib_targets_by_price else None

        # FVG'ye en yakın Fibonacci seviyesini bul
        fib_targets.sort(key=lambda x: x['distance_to_fvg'])
        closest_fib_to_fvg = fib_targets[0]

        # Akıllı seçim: Fiyat ile FVG arasındaki konuma göre karar ver
        fvg_eq = closest_fvg['eq']

        # Fiyat FVG'den uzaksa, fiyata daha yakın Fibonacci seviyesini tercih et
        price_to_fvg_distance = abs(last_price - fvg_eq) / last_price

        if price_to_fvg_distance > self.fvg_price_distance_threshold:  # Merkezileştirilmiş eşik
            # Fiyata daha yakın Fibonacci seviyesini kullan
            closest_fib = closest_fib_to_price
            selection_reason = "fiyata_yakin"
            logger.debug(f"[{symbol}] FVG uzak (%{price_to_fvg_distance*100:.2f}), fiyata yakın Fib seçildi: {closest_fib['level']} ({closest_fib['price']:.4f})")
        else:
            # FVG'ye yakın Fibonacci seviyesini kullan
            closest_fib = closest_fib_to_fvg
            selection_reason = "fvg_yakin"
            logger.debug(f"[{symbol}] FVG yakın (%{price_to_fvg_distance*100:.2f}), FVG'ye yakın Fib seçildi: {closest_fib['level']} ({closest_fib['price']:.4f})")

        # FVG + Fibonacci kombinasyonu ile giriş seviyesi belirle
        # Strateji: FVG EQ seviyesi ile Fibonacci seviyesinin ortalaması
        fib_price = closest_fib['price']

        # Ağırlıklı ortalama (merkezileştirilmiş ağırlıklar)
        combined_entry = (fvg_eq * self.fvg_weight) + (fib_price * self.fibonacci_weight)

        # Alternatif olarak FVG bölgesinin sınırlarını da kullan
        fvg_top = closest_fvg['top']
        fvg_bottom = closest_fvg['bottom']

        # Trade direction'a göre FVG bölgesi içinde giriş noktası belirle (merkezileştirilmiş pozisyon)
        if trade_direction == "bull":
            # Bullish için FVG'nin alt kısmına yakın giriş
            fvg_entry = fvg_bottom + ((fvg_top - fvg_bottom) * self.fvg_entry_position)
        else:
            # Bearish için FVG'nin üst kısmına yakın giriş
            fvg_entry = fvg_top - ((fvg_top - fvg_bottom) * self.fvg_entry_position)

        # Final giriş seviyesi: Combined entry ile FVG entry'nin ortalaması
        final_entry = (combined_entry + fvg_entry) / 2

        # Trading mantığı kontrolü - Giriş seviyesi mantıklı mı?
        if trade_direction == "bull" and final_entry > last_price:
            logger.debug(f"[{symbol}] FVG+Fib girişi ({final_entry:.4f}) mevcut fiyattan ({last_price:.4f}) yüksek, atlanıyor.")
            return None

        if trade_direction == "bear" and final_entry < last_price:
            logger.debug(f"[{symbol}] FVG+Fib girişi ({final_entry:.4f}) mevcut fiyattan ({last_price:.4f}) alçak, atlanıyor.")
            return None



        entry_levels = {
            "primary_entry": final_entry,
            "secondary_entry": fib_price,  # Alternatif olarak pure Fibonacci
            "fvg_eq": fvg_eq,
            "fvg_top": fvg_top,
            "fvg_bottom": fvg_bottom,
            "fib_level": str(closest_fib['level']),
            "volatility_level": volatility_level,
            "strategy_details": f"FVG_EQ:{fvg_eq:.4f}+Fib_{closest_fib['level']}:{fib_price:.4f}({selection_reason})"
        }

        if atr_percentage is not None:
            entry_levels["atr_percentage"] = atr_percentage

        return entry_levels

    def _calculate_fibonacci_entry(self,
                                  symbol: str,
                                  fibonacci_data: Dict[str, Any],
                                  last_price: float,
                                  trade_direction: str,
                                  volatility_level: str = "unknown",
                                  atr_percentage: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        4H Fibonacci seviyelerini kullanarak giriş seviyeleri hesaplar.
        Volatilite seviyesine göre giriş noktalarını dinamik olarak ayarlar.

        Düşük volatilite: Fiyata en yakın Fibonacci seviyesi
        Orta volatilite: Fiyata en yakın Fibonacci seviyesi
        Yüksek volatilite: Bir sonraki güvenli Fibonacci seviyesi

        Bullish için: 0.382, 0.5, 0.618, 0.705, 0.786, 0.886
        Bearish için: 0.114, 0.214, 0.295, 0.382, 0.5, 0.618

        Args:
            symbol: Kripto para sembolü
            fibonacci_data: Fibonacci analiz sonuçları (all_timeframe_data[symbol]['240']['fib_levels'])
            last_price: Son fiyat
            trade_direction: İşlem yönü ('bull' veya 'bear')
            volatility_level: Volatilite seviyesi ('low', 'medium', 'high', 'unknown')
            atr_percentage: ATR yüzdesi (opsiyonel)

        Returns:
            Dict: Hesaplanan giriş seviyeleri veya None
        """
        entry_levels = {}

        # Fibonacci seviyelerini kontrol et
        levels = fibonacci_data.get('levels', {})
        if not isinstance(levels, dict) or not levels:
            logger.debug(f"[{symbol}] Fibonacci seviyeleri bulunamadı.")
            return None

        # Seviyeleri float olarak al
        float_levels = {}
        for k, v in levels.items():
            try:
                float_key = float(k)
                float_levels[float_key] = v
            except Exception as ex:
                logger.error(f"[{symbol}] Fibonacci seviyesi anahtarı float'a çevrilemedi: {k} ({ex})")

        # Bullish ve bearish için kullanılacak seviyeler
        bull_levels = [0.382, 0.5, 0.618, 0.705, 0.786, 0.886]
        bear_levels = [0.114, 0.214, 0.295, 0.382, 0.5, 0.618]

        fibonacci_targets = []

        if trade_direction == "bull":
            # BULL işlemler için: Mevcut fiyatın ALTINDAKI (gerideki) fibonacci seviyelerini seç
            for level in bull_levels:
                if level in float_levels:
                    level_price = float_levels[level]['price']
                    # Sadece mevcut fiyatın altındaki seviyeleri dahil et
                    if level_price < last_price:
                        fibonacci_targets.append({
                            "type": "level",
                            "name": str(level),
                            "primary_entry": level_price,
                            "secondary_entry": None,
                            "distance": abs(last_price - level_price) / last_price if last_price != 0 else float('inf')
                        })
        elif trade_direction == "bear":
            # BEAR işlemler için: Mevcut fiyatın ÜSTÜNDEKI (ilerideki) fibonacci seviyelerini seç
            for level in bear_levels:
                if level in float_levels:
                    level_price = float_levels[level]['price']
                    # Sadece mevcut fiyatın üstündeki seviyeleri dahil et
                    if level_price > last_price:
                        fibonacci_targets.append({
                            "type": "level",
                            "name": str(level),
                            "primary_entry": level_price,
                            "secondary_entry": None,
                            "distance": abs(last_price - level_price) / last_price if last_price != 0 else float('inf')
                        })
        else:
            logger.error(f"[{symbol}] trade_direction bilinmiyor: {trade_direction}")
            return None

        if not fibonacci_targets:
            logger.debug(f"[{symbol}] Uygun Fibonacci giriş seviyesi bulunamadı (mevcut fiyat: {last_price:.4f}, yön: {trade_direction}).")
            return None

        # Debug: Filtrelenmiş fibonacci seviyelerini logla
        fib_levels_str = ", ".join([f"{t['name']}:{t['primary_entry']:.4f}" for t in fibonacci_targets])
        logger.debug(f"[{symbol}] Filtrelenmiş Fib seviyeleri ({trade_direction}): {fib_levels_str}")

        # Fiyata göre sırala (en yakından en uzağa)
        fibonacci_targets.sort(key=lambda x: x["distance"])

        # Önce en yakın fibonacci seviyesini al
        if not fibonacci_targets:
            logger.debug(f"[{symbol}] Fibonacci hedefleri bulunamadı.")
            return None

        closest_target = fibonacci_targets[0]  # En yakın seviye

        # Mesafe kontrolü - en yakın seviye çok uzaksa uyarı ver
        MAX_DISTANCE_PCT = 0.013  # %1.3 maksimum mesafe sınırı
        closest_distance = closest_target["distance"]

        if closest_distance > MAX_DISTANCE_PCT:
            logger.warning(f"[{symbol}] En yakın Fib seviyesi çok uzak (%{closest_distance*100:.2f}), yine de kullanılıyor.")

        # Volatilite algoritması: Volatilite seviyesine göre Fibonacci seviyesi seç
        selected_target = None

        if volatility_level == "low":
            # Düşük volatilite: Fiyata en yakın Fibonacci seviyesi
            selected_target = closest_target
            logger.debug(f"[{symbol}] Düşük volatilite - fiyata en yakın seviye seçildi: {selected_target['name']}")

        elif volatility_level == "medium":
            # Orta volatilite: Fiyata en yakın Fibonacci seviyesi
            selected_target = closest_target
            logger.debug(f"[{symbol}] Orta volatilite - fiyata en yakın seviye seçildi: {selected_target['name']}")

        elif volatility_level == "high":
            # Yüksek volatilite: Bir sonraki güvenli Fibonacci seviyesi
            if len(fibonacci_targets) >= 2:
                selected_target = fibonacci_targets[1]  # İkinci en yakın seviye
                logger.debug(f"[{symbol}] Yüksek volatilite - bir sonraki güvenli seviye seçildi: {selected_target['name']}")
            else:
                selected_target = closest_target
                logger.debug(f"[{symbol}] Yüksek volatilite - sadece bir seviye mevcut, en yakın kullanılıyor: {selected_target['name']}")

        else:
            # Bilinmeyen volatilite: Varsayılan olarak en yakın seviye
            selected_target = closest_target
            logger.debug(f"[{symbol}] Bilinmeyen volatilite - varsayılan en yakın seviye kullanılıyor: {selected_target['name']}")

        # Seçilen seviyenin geçerliliğini kontrol et
        if selected_target is None:
            logger.error(f"[{symbol}] Volatilite algoritması sonucu seviye seçilemedi!")
            selected_target = closest_target  # Fallback

        # Seçilen seviyenin adını ve volatilite bilgisini ekle
        level_name = selected_target['name']

        # Debug: Seçilen fibonacci seviyesini logla
        distance_pct = selected_target['distance'] * 100
        logger.debug(f"[{symbol}] Seçilen Fib seviyesi: {level_name} ({selected_target['primary_entry']:.4f}) - Mesafe: %{distance_pct:.2f} - Volatilite: {volatility_level}")

        # Alternatif giriş seviyesi olarak bir sonraki seviyeyi de ekle (eğer varsa)
        selected_target["secondary_entry"] = None
        if len(fibonacci_targets) > 1:
            secondary_target = fibonacci_targets[1]  # İkinci en yakın seviye
            selected_target["secondary_entry"] = secondary_target["primary_entry"]

        entry_levels["primary_entry"] = selected_target["primary_entry"]
        entry_levels["secondary_entry"] = selected_target["secondary_entry"]
        entry_levels["fib_level"] = level_name
        entry_levels["volatility_level"] = volatility_level
        if atr_percentage is not None:
            entry_levels["atr_percentage"] = atr_percentage

        return entry_levels

    def _calculate_tp_levels(self, entry_price: float, sl_amount: float, trade_direction: str, fibonacci_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Fibonacci seviyelerini ve R:R oranlarını kullanarak TP1, TP2 ve TP3 seviyelerini hesaplar.

        TP1 = 1:1 R:R (Stop mesafesi kadar)
        TP2 = Bir sonraki Fibonacci seviyesi veya 1.5:1 R:R
        TP3 = Bir sonraki Fibonacci seviyesi veya 2:1 R:R

        Args:
            entry_price: Giriş fiyatı
            sl_amount: Stop loss mesafesi (mutlak değer)
            trade_direction: İşlem yönü ('bull' veya 'bear')
            fibonacci_data: Fibonacci analiz sonuçları

        Returns:
            Dict: TP1, TP2 ve TP3 seviyelerini içeren sözlük
        """
        tp_levels = {
            "tp1": None,
            "tp2": None,
            "tp3": None
        }

        # TP1: 1:1 R:R (Stop mesafesi kadar)
        if trade_direction == "bull":
            tp_levels["tp1"] = entry_price + sl_amount  # 1:1 R:R
        elif trade_direction == "bear":
            tp_levels["tp1"] = entry_price - sl_amount  # 1:1 R:R

        # Fibonacci seviyelerini kullanarak TP2 ve TP3 hesapla
        if fibonacci_data is not None and isinstance(fibonacci_data, dict) and "levels" in fibonacci_data:
            # Fibonacci seviyelerini al
            fib_levels = fibonacci_data.get("levels", {})
            if not isinstance(fib_levels, dict):
                logger.warning(f"Fibonacci seviyeleri geçerli bir sözlük değil: {type(fib_levels)}")
                fib_levels = {}

            # Seviyeleri float olarak al
            float_levels = {}
            for k, v in fib_levels.items():
                try:
                    float_key = float(k)
                    float_levels[float_key] = v
                except Exception as ex:
                    logger.error(f"Fibonacci seviyesi anahtarı float'a çevrilemedi: {k} ({ex})")

            # Bullish ve bearish için kullanılacak seviyeler (TP hesaplaması için genişletilmiş)
            bull_levels = [0.295, 0.382, 0.5, 0.618, 0.705, 0.786, 0.886, 1.0, 1.114, 1.214, 1.618]
            bear_levels = [0.786, 0.618, 0.5, 0.382, 0.295, 0.214, 0.114, 0.0, -0.114, -0.214, -0.618]

            # Kullanılacak seviyeleri seç
            target_levels = bull_levels if trade_direction == "bull" else bear_levels

            # Entry fiyatına göre Fibonacci seviyelerini sırala
            fib_targets = []
            for level in target_levels:
                if level in float_levels:
                    level_price = float_levels[level]['price']
                    # Bullish için entry'den yüksek, bearish için entry'den düşük seviyeleri al
                    if (trade_direction == "bull" and level_price > entry_price) or (trade_direction == "bear" and level_price < entry_price):
                        fib_targets.append({
                            "level": level,
                            "price": level_price,
                            "distance": abs(entry_price - level_price)
                        })

            # Mesafeye göre sırala (en yakından en uzağa)
            fib_targets.sort(key=lambda x: x["distance"])

            # TP2 ve TP3 için Fibonacci seviyelerini kullan
            if len(fib_targets) >= 1:
                # TP2: TP1 + Bir sonraki Fibonacci seviyesi
                tp2_fib = fib_targets[0]["price"]
                if trade_direction == "bull":
                    tp_levels["tp2"] = max(tp_levels["tp1"], tp2_fib)  # Daha yüksek olanı al
                else:
                    tp_levels["tp2"] = min(tp_levels["tp1"], tp2_fib)  # Daha düşük olanı al

                # TP3: TP2 + Bir sonraki Fibonacci seviyesi
                if len(fib_targets) >= 2:
                    tp3_fib = fib_targets[1]["price"]
                    if trade_direction == "bull":
                        tp_levels["tp3"] = max(tp_levels["tp2"], tp3_fib)  # Daha yüksek olanı al
                    else:
                        tp_levels["tp3"] = min(tp_levels["tp2"], tp3_fib)  # Daha düşük olanı al

        # Fibonacci seviyeleri yoksa veya kullanılamazsa, merkezileştirilmiş R:R oranları kullan
        if tp_levels["tp2"] is None:
            if trade_direction == "bull":
                tp_levels["tp2"] = entry_price + (sl_amount * self.tp2_rr_ratio)
            else:
                tp_levels["tp2"] = entry_price - (sl_amount * self.tp2_rr_ratio)

        if tp_levels["tp3"] is None:
            if trade_direction == "bull":
                tp_levels["tp3"] = entry_price + (sl_amount * self.tp3_rr_ratio)
            else:
                tp_levels["tp3"] = entry_price - (sl_amount * self.tp3_rr_ratio)

        # TP seviyelerini logla
        logger.debug(f"[TP] TP1={tp_levels['tp1']:.4f}, TP2={tp_levels['tp2']:.4f}, TP3={tp_levels['tp3']:.4f}")

        return tp_levels

    def _calculate_sl_tp(self, entry_price: float, trade_direction: str, swing_points: Optional[List[Dict[str, Any]]] = None, pattern_name: Optional[str] = None, fibonacci_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Stop Loss ve Take Profit seviyelerini hesaplar.

        Market Structure Break (MSB) mantığına göre stop loss hesaplanır:
        - BULL işlemler için: Son LL (Lower Low) seviyesi + %1 likidite payı
        - BEAR işlemler için: Son HH (Higher High) seviyesi + %1 likidite payı
        - Triple top (TRIT) ve Triple Bottom (TRIB) patternleri için sabit %2.5 stop loss
        - MSB seviyesi bulunamazsa varsayılan %2.5 stop loss

        Args:
            entry_price: Giriş fiyatı
            trade_direction: İşlem yönü ('bull' veya 'bear')
            swing_points: 4H zaman dilimindeki pivot noktaları (swing high/low) listesi
            pattern_name: Tespit edilen pattern adı (TRIT, TRIB vb.)
            fibonacci_data: Fibonacci analiz sonuçları (TP seviyeleri için)

        Returns:
            Dict: SL ve TP seviyeleri
        """
        # Merkezileştirilmiş SL eşikleri kullan
        # (Artık __init__'te tanımlı değerler kullanılıyor)

        # Stop loss hesaplama başlangıç
        logger.debug(f"[SL] Entry: {entry_price:.4f}, Direction: {trade_direction}, Pattern: {pattern_name}")

        # Varsayılan değerler
        sl_tp = {
            "stop_loss": None,
            "take_profit": None,
            "sl_percentage": None,
            "tp_percentage": None,
            "sl_type": "default"  # Stop loss tipi (default, pivot veya triple_pattern)
        }

        # Triple pattern kontrolü
        is_triple_pattern = pattern_name in ["TRIT", "TRIB"]

        # Triple pattern için sabit stop loss hesaplama
        if is_triple_pattern:
            # Merkezileştirilmiş sabit stop loss yüzdesi
            sl_pct = self.triple_pattern_sl_pct

            if trade_direction == "bull":
                # BULL için, entry'nin sabit yüzde altında stop
                sl_tp["stop_loss"] = entry_price * (1 - sl_pct)
            elif trade_direction == "bear":
                # BEAR için, entry'nin sabit yüzde üstünde stop
                sl_tp["stop_loss"] = entry_price * (1 + sl_pct)

            sl_tp["sl_type"] = "triple_pattern"
            logger.info(f"[SL] ✅ Triple pattern SL: {sl_tp['stop_loss']:.4f} (%{sl_pct*100:.1f})")

        # Triple pattern değilse market structure break tabanlı stop loss hesaplama
        else:
            # Market Structure Break tabanlı stop loss hesaplama
            msb_sl = None
            msb_found = False

            # Swing noktaları var mı kontrol et (market yapısı analizi için en az 3 swing noktası gerekli)
            if swing_points and len(swing_points) >= 3:
                # Market Structure Break mantığına göre stop loss hesapla
                # Son üç swing noktasını al (market yapısı analizi için)
                recent_swings = swing_points[-3:] if len(swing_points) >= 3 else swing_points
                # Market yapısı kırılması için kritik seviyeleri belirle
                critical_level = None
                critical_type = None

                if trade_direction == "bull":
                    # BULL için: Son LL (Lower Low) seviyesini bul
                    # LL seviyesi kırılırsa market yapısı bozulur ve işlem geçersiz olur
                    for swing in reversed(recent_swings):  # En yeniden eskiye doğru
                        swing_type = swing.get("type")
                        pivot_price = swing.get("price")

                        if swing_type == "LL" and pivot_price is not None:
                            critical_level = pivot_price
                            critical_type = "LL"
                            logger.debug(f"[SL] BULL kritik LL bulundu: {critical_level:.4f}")
                            break

                elif trade_direction == "bear":
                    # BEAR için: Son HH (Higher High) seviyesini bul
                    # HH seviyesi kırılırsa market yapısı bozulur ve işlem geçersiz olur
                    for swing in reversed(recent_swings):  # En yeniden eskiye doğru
                        swing_type = swing.get("type")
                        pivot_price = swing.get("price")

                        if swing_type == "HH" and pivot_price is not None:
                            critical_level = pivot_price
                            critical_type = "HH"
                            logger.debug(f"[SL] BEAR kritik HH bulundu: {critical_level:.4f}")
                            break

                # Kritik seviye bulunduysa stop loss hesapla
                if critical_level is not None:
                    # Kritik seviyenin entry'e göre konumunu kontrol et
                    if (trade_direction == "bull" and critical_level < entry_price) or (trade_direction == "bear" and critical_level > entry_price):
                        # Merkezileştirilmiş likidite payı
                        buffer_pct = self.liquidity_buffer_pct

                        # Market yapısı kırılma seviyesini stop loss olarak kullan
                        if trade_direction == "bull":
                            # BULL için, kritik LL seviyesinin altında stop
                            msb_sl = critical_level * (1 - buffer_pct)
                        else:  # bear
                            # BEAR için, kritik HH seviyesinin üstünde stop
                            msb_sl = critical_level * (1 + buffer_pct)

                        msb_found = True
                        logger.debug(f"[SL] MSB SL: {critical_type}={critical_level:.4f}, SL={msb_sl:.4f}")
                    else:
                        logger.debug(f"[SL] Kritik {critical_type} seviye uygun konumda değil: {critical_level:.4f}")
            else:
                logger.debug(f"[SL] Yetersiz swing point: {len(swing_points) if swing_points else 0} < 3")

            # Market yapısı seviyesi bulunamazsa varsayılan stop loss kullan
            if not msb_found:
                # Merkezileştirilmiş varsayılan stop loss hesaplama

                if trade_direction == "bull":
                    # BULL için, entry'nin sabit yüzde altında stop
                    sl_tp["stop_loss"] = entry_price * (1 - self.default_sl_pct)
                elif trade_direction == "bear":
                    # BEAR için, entry'nin sabit yüzde üstünde stop
                    sl_tp["stop_loss"] = entry_price * (1 + self.default_sl_pct)

                sl_tp["sl_type"] = "default"
                logger.info(f"[SL] ✅ Varsayılan SL: {sl_tp['stop_loss']:.4f} (%{self.default_sl_pct*100:.1f})")
            else:
                # Market yapısı seviyesi bulunduysa, MSB tabanlı stop loss kullan
                sl_tp["stop_loss"] = msb_sl
                sl_tp["sl_type"] = "market_structure"
                logger.info(f"[SL] ✅ MSB SL: {sl_tp['stop_loss']:.4f}")


        # SL Yüzdesini Hesapla
        if sl_tp["stop_loss"] is not None:
            if trade_direction == "bull":
                # Bull için SL entry'den düşükte
                sl_tp["sl_percentage"] = ((sl_tp["stop_loss"] / entry_price) - 1) * 100  # Negatif değer
            elif trade_direction == "bear":
                # Bear için SL entry'den yüksekte
                sl_tp["sl_percentage"] = ((sl_tp["stop_loss"] / entry_price) - 1) * 100  # Pozitif değer

            # Final stop loss sonucunu logla
            logger.debug(f"[SL] Final: {sl_tp['stop_loss']:.4f} ({sl_tp['sl_percentage']:.2f}%) - {sl_tp['sl_type']}")
        else:
            logger.error(f"[SL] ❌ Stop loss hesaplanamadı!")

        # Take Profit hesaplama (TP1, TP2, TP3 seviyeleri)
        if sl_tp["stop_loss"] is not None:
            # Stop loss mesafesi
            sl_amount = abs(entry_price - sl_tp["stop_loss"])

            # Fibonacci seviyelerini kullanarak TP seviyeleri hesapla
            try:
                tp_levels = self._calculate_tp_levels(
                    entry_price=entry_price,
                    sl_amount=sl_amount,
                    trade_direction=trade_direction,
                    fibonacci_data=fibonacci_data  # Fonksiyona geçirilen fibonacci_data parametresini kullan
                )
            except Exception as e:
                logger.error(f"TP seviyeleri hesaplanırken hata: {e}")
                # Hata durumunda merkezileştirilmiş varsayılan TP seviyeleri hesapla
                tp_levels = {
                    "tp1": entry_price + sl_amount if trade_direction == "bull" else entry_price - sl_amount,
                    "tp2": entry_price + (sl_amount * self.tp2_rr_ratio) if trade_direction == "bull" else entry_price - (sl_amount * self.tp2_rr_ratio),
                    "tp3": entry_price + (sl_amount * self.tp3_rr_ratio) if trade_direction == "bull" else entry_price - (sl_amount * self.tp3_rr_ratio)
                }

            # TP seviyelerini sl_tp sözlüğüne ekle
            sl_tp["take_profit"] = tp_levels.get("tp1")  # Ana TP olarak TP1'i kullan (geriye uyumluluk için)
            sl_tp["tp1"] = tp_levels.get("tp1")
            sl_tp["tp2"] = tp_levels.get("tp2")
            sl_tp["tp3"] = tp_levels.get("tp3")

            # TP Yüzdelerini Hesapla
            if sl_tp["take_profit"] is not None:
                if trade_direction == "bull":
                    sl_tp["tp_percentage"] = ((sl_tp["take_profit"] / entry_price) - 1) * 100  # Pozitif değer
                elif trade_direction == "bear":
                    sl_tp["tp_percentage"] = ((sl_tp["take_profit"] / entry_price) - 1) * 100  # Negatif değer

            # TP1, TP2, TP3 yüzdelerini hesapla
            for tp_key in ["tp1", "tp2", "tp3"]:
                if sl_tp[tp_key] is not None:
                    if trade_direction == "bull":
                        sl_tp[f"{tp_key}_percentage"] = ((sl_tp[tp_key] / entry_price) - 1) * 100  # Pozitif değer
                    elif trade_direction == "bear":
                        sl_tp[f"{tp_key}_percentage"] = ((sl_tp[tp_key] / entry_price) - 1) * 100  # Negatif değer

        return sl_tp
