from typing import Dict, List, Any, Optional
from loguru import logger
import pandas as pd
import numpy as np
from pivot_analyzer import PivotAnalyzer

class SRFlipAnalyzer:
    """
    Destek/Direnç dönüşümlerini (S/R Flip) analiz eder.

    S-R.txt algoritmasından ilham alınarak geliştirilmiş kapsamlı S/R analizi:
    - Pivot noktalarını kullanarak S/R seviyelerini tespit eder
    - Kanal genişliği kontrolü yapar
    - OHLC doğrulama ile seviye gücünü hesaplar
    - S/R Flip durumlarını tespit eder

    Mantık:
    - Bullish S/R Flip: Önceden direnç olan seviyenin kırıldıktan sonra destek olarak test edilmesi
    - Bearish S/R Flip: Önceden destek olan seviyenin kırıldıktan sonra direnç olarak test edilmesi
    """

    def __init__(self,
                 tolerance_pct: float = 0.005,
                 loopback_period: int = 240,
                 max_channel_width_pct: float = 5.0,
                 min_strength: int = 2,
                 ohlc_validation_bars: int = 240):
        """
        SRFlipAnalyzer sınıfını başlatır.

        Args:
            tolerance_pct (float): Bir seviyenin test edildiğini kabul etmek için yüzde tolerans (%0.5)
            loopback_period (int): Geriye dönük analiz periyodu (250 bar)
            max_channel_width_pct (float): Maksimum kanal genişliği yüzdesi (%6.0)
            min_strength (int): Minimum güç skoru (2)
            ohlc_validation_bars (int): OHLC doğrulama için kullanılacak bar sayısı (240)
        """
        self.tolerance_pct = tolerance_pct
        self.loopback_period = loopback_period
        self.max_channel_width_pct = max_channel_width_pct
        self.min_strength = min_strength
        self.ohlc_validation_bars = ohlc_validation_bars

        # Günlük timeframe için pivot analyzer
        self.pivot_analyzer = PivotAnalyzer(timeframe="D")

        logger.info(f"S/R Flip Analyzer başlatıldı:")
        logger.info(f"  └─ Tolerans: %{tolerance_pct*100:.1f}")
        logger.info(f"  └─ Loopback Period: {loopback_period}")
        logger.info(f"  └─ Max Channel Width: %{max_channel_width_pct:.1f}")
        logger.info(f"  └─ Min Strength: {min_strength}")
        logger.info(f"  └─ OHLC Validation: {ohlc_validation_bars} bars")

    def analyze(self, last_price: float, candles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        OHLC mum verilerini kullanarak kapsamlı S/R analizi yapar.

        S-R.txt algoritmasından ilham alınarak:
        1. Pivot noktalarını hesaplar
        2. S/R seviyelerini ve güçlerini belirler
        3. OHLC doğrulama yapar
        4. S/R Flip durumlarını tespit eder

        Args:
            last_price (float): Mevcut son fiyat
            candles (List[Dict]): OHLC mum verileri listesi

        Returns:
            dict: {
                'sr_levels': [{'high': float, 'low': float, 'strength': int, 'type': str}],
                'bullish_flips': [float],
                'bearish_flips': [float],
                'current_zone': str  # 'support', 'resistance', 'neutral'
            }
        """
        if candles is None or len(candles) < self.loopback_period:
            logger.warning(f"S/R analizi için yeterli mum verisi yok: {len(candles) if candles is not None else 0} < {self.loopback_period}")
            return {'sr_levels': [], 'bullish_flips': [], 'bearish_flips': [], 'current_zone': 'neutral'}

        try:
            # Mum verilerini DataFrame'e çevir (eğer zaten DataFrame değilse)
            if isinstance(candles, pd.DataFrame):
                df = candles.copy()
            else:
                df = pd.DataFrame(candles)

            df['high'] = pd.to_numeric(df['high'])
            df['low'] = pd.to_numeric(df['low'])
            df['close'] = pd.to_numeric(df['close'])
            df['open'] = pd.to_numeric(df['open'])
            df['volume'] = pd.to_numeric(df['volume'])

            # Son loopback_period kadar veriyi al
            df = df.tail(self.loopback_period).reset_index(drop=True)

            # Pivot noktalarını hesapla
            df_with_pivots = self.pivot_analyzer.find_pivots(df)

            # Pivot değerlerini çıkar
            pivot_values = self._extract_pivot_values(df_with_pivots)

            if not pivot_values:
                logger.debug("Pivot noktaları bulunamadı")
                return {'sr_levels': [], 'bullish_flips': [], 'bearish_flips': [], 'current_zone': 'neutral'}

            # S/R seviyelerini hesapla
            sr_levels = self._calculate_sr_levels(pivot_values, df)

            # OHLC doğrulama ile güçleri hesapla
            sr_levels = self._validate_with_ohlc(sr_levels, df)

            # En güçlü seviyeleri filtrele
            strong_levels = self._filter_strongest_levels(sr_levels)

            # S/R Flip durumlarını tespit et
            bullish_flips, bearish_flips = self._detect_sr_flips(strong_levels, last_price)

            # Mevcut fiyatın hangi bölgede olduğunu belirle
            current_zone = self._determine_current_zone(strong_levels, last_price)

            logger.info(f"S/R Analizi tamamlandı: {len(strong_levels)} güçlü seviye, {len(bullish_flips)} bullish flip, {len(bearish_flips)} bearish flip")

            return {
                'sr_levels': strong_levels,
                'bullish_flips': bullish_flips,
                'bearish_flips': bearish_flips,
                'current_zone': current_zone
            }

        except Exception as e:
            logger.error(f"S/R analizi sırasında hata: {e}")
            return {'sr_levels': [], 'bullish_flips': [], 'bearish_flips': [], 'current_zone': 'neutral'}

    def _extract_pivot_values(self, df: pd.DataFrame) -> List[float]:
        """
        DataFrame'den pivot değerlerini çıkarır.

        Args:
            df: Pivot bilgileri içeren DataFrame

        Returns:
            List[float]: Pivot fiyat değerleri listesi
        """
        pivot_values = []

        # Yüksek pivotları ekle
        high_pivots = df[df['pivot_high'].notna()]['pivot_high'].values
        pivot_values.extend(high_pivots)

        # Düşük pivotları ekle
        low_pivots = df[df['pivot_low'].notna()]['pivot_low'].values
        pivot_values.extend(low_pivots)

        # Benzersiz değerleri döndür
        unique_pivots = list(set(pivot_values))
        logger.debug(f"Çıkarılan pivot değerleri: {len(unique_pivots)} adet")

        return unique_pivots

    def _calculate_sr_levels(self, pivot_values: List[float], df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        S-R.txt algoritmasına göre S/R seviyelerini hesaplar.

        Args:
            pivot_values: Pivot fiyat değerleri
            df: OHLC verileri

        Returns:
            List[Dict]: S/R seviye bilgileri
        """
        if not pivot_values:
            return []

        # Maksimum kanal genişliğini hesapla
        highest_price = max(pivot_values)
        lowest_price = min(pivot_values)
        max_channel_width = (highest_price - lowest_price) * self.max_channel_width_pct / 100

        sr_levels = []

        # Her pivot için S/R seviyesi hesapla
        for i, base_pivot in enumerate(pivot_values):
            high_level = base_pivot
            low_level = base_pivot
            strength = 0

            # Diğer pivotlarla kanal oluştur
            for other_pivot in pivot_values:
                if other_pivot == base_pivot:
                    continue

                # Kanal genişliği kontrolü
                width = abs(other_pivot - base_pivot)
                if width <= max_channel_width:
                    # Kanal sınırlarını güncelle
                    if other_pivot > high_level:
                        high_level = other_pivot
                    elif other_pivot < low_level:
                        low_level = other_pivot

                    # Her pivot 20 puan güç ekler (S-R.txt'deki gibi)
                    strength += 20

            # Minimum güç kontrolü
            if strength >= self.min_strength * 20:
                sr_level = {
                    'high': high_level,
                    'low': low_level,
                    'strength': strength,
                    'base_pivot': base_pivot,
                    'width': high_level - low_level
                }
                sr_levels.append(sr_level)

        logger.debug(f"Hesaplanan S/R seviyeleri: {len(sr_levels)} adet")
        return sr_levels

    def _validate_with_ohlc(self, sr_levels: List[Dict[str, Any]], df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        OHLC verilerini kullanarak S/R seviyelerinin gücünü doğrular.

        Args:
            sr_levels: S/R seviye listesi
            df: OHLC verileri

        Returns:
            List[Dict]: Doğrulanmış S/R seviyeleri
        """
        # Son ohlc_validation_bars kadar veriyi al
        validation_df = df.tail(self.ohlc_validation_bars)

        for sr_level in sr_levels:
            high_level = sr_level['high']
            low_level = sr_level['low']
            ohlc_touches = 0

            # Her bar için OHLC değerlerini kontrol et
            for _, row in validation_df.iterrows():
                ohlc_values = [row['open'], row['high'], row['low'], row['close']]

                for value in ohlc_values:
                    if low_level <= value <= high_level:
                        ohlc_touches += 1
                        break  # Aynı bar için sadece bir kez say

            # OHLC dokunuşlarını güce ekle
            sr_level['strength'] += ohlc_touches
            sr_level['ohlc_touches'] = ohlc_touches

        return sr_levels

    def _filter_strongest_levels(self, sr_levels: List[Dict[str, Any]], max_levels: int = 6) -> List[Dict[str, Any]]:
        """
        En güçlü S/R seviyelerini filtreler.

        Args:
            sr_levels: S/R seviye listesi
            max_levels: Maksimum seviye sayısı

        Returns:
            List[Dict]: En güçlü seviyeler
        """
        # Güce göre sırala (en güçlüden en zayıfa)
        sorted_levels = sorted(sr_levels, key=lambda x: x['strength'], reverse=True)

        # En güçlü seviyeleri al
        strongest_levels = sorted_levels[:max_levels]

        # Her seviyenin tipini belirle (support/resistance)
        for level in strongest_levels:
            level['type'] = 'resistance' if level['high'] > level['low'] else 'support'

        logger.debug(f"En güçlü {len(strongest_levels)} seviye seçildi")
        return strongest_levels

    def _detect_sr_flips(self, sr_levels: List[Dict[str, Any]], last_price: float) -> tuple:
        """
        S/R seviyelerinden flip durumlarını tespit eder.

        Args:
            sr_levels: S/R seviye listesi
            last_price: Mevcut fiyat

        Returns:
            tuple: (bullish_flips, bearish_flips)
        """
        bullish_flips = []
        bearish_flips = []

        for level in sr_levels:
            high_level = level['high']
            low_level = level['low']
            mid_level = (high_level + low_level) / 2

            # Fiyatın seviyeye olan mesafesi
            distance_to_high = abs(last_price - high_level) / high_level
            distance_to_low = abs(last_price - low_level) / low_level
            distance_to_mid = abs(last_price - mid_level) / mid_level

            # Bullish Flip: Eski direnç seviyesi artık destek olarak test ediliyor
            if (distance_to_high < self.tolerance_pct and last_price >= high_level * (1 - self.tolerance_pct)) or \
               (distance_to_mid < self.tolerance_pct and last_price >= mid_level * (1 - self.tolerance_pct) and level.get('type') == 'resistance'):
                bullish_flips.append(high_level)
                logger.debug(f"Bullish S/R Flip: {high_level:.4f} (Mesafe: %{distance_to_high*100:.2f})")

            # Bearish Flip: Eski destek seviyesi artık direnç olarak test ediliyor
            if (distance_to_low < self.tolerance_pct and last_price <= low_level * (1 + self.tolerance_pct)) or \
               (distance_to_mid < self.tolerance_pct and last_price <= mid_level * (1 + self.tolerance_pct) and level.get('type') == 'support'):
                bearish_flips.append(low_level)
                logger.debug(f"Bearish S/R Flip: {low_level:.4f} (Mesafe: %{distance_to_low*100:.2f})")

        return list(set(bullish_flips)), list(set(bearish_flips))

    def _determine_current_zone(self, sr_levels: List[Dict[str, Any]], last_price: float) -> str:
        """
        Mevcut fiyatın hangi S/R bölgesinde olduğunu belirler.

        Args:
            sr_levels: S/R seviye listesi
            last_price: Mevcut fiyat

        Returns:
            str: 'support', 'resistance', 'neutral'
        """
        for level in sr_levels:
            high_level = level['high']
            low_level = level['low']

            # Fiyat seviye içinde mi?
            if low_level <= last_price <= high_level:
                # Seviyenin tipine göre bölgeyi belirle
                if level.get('type') == 'resistance':
                    return 'resistance'
                elif level.get('type') == 'support':
                    return 'support'
                else:
                    # Fiyatın seviye içindeki konumuna göre belirle
                    mid_level = (high_level + low_level) / 2
                    if last_price > mid_level:
                        return 'resistance'
                    else:
                        return 'support'

        return 'neutral'

    # Eski fonksiyonları uyumluluk için koruyalım
    def _detect_bullish_flips(self, high_pivots: List[Dict[str, Any]], last_price: float) -> List[float]:
        """
        Geriye uyumluluk için korunan eski fonksiyon.
        """
        logger.warning("Eski _detect_bullish_flips fonksiyonu kullanılıyor. Yeni analyze() fonksiyonunu kullanın.")
        return []

    def _detect_bearish_flips(self, low_pivots: List[Dict[str, Any]], last_price: float) -> List[float]:
        """
        Geriye uyumluluk için korunan eski fonksiyon.
        """
        logger.warning("Eski _detect_bearish_flips fonksiyonu kullanılıyor. Yeni analyze() fonksiyonunu kullanın.")
        return []

    def analyze_legacy(self, last_price: float, pivots: Dict[str, List[Dict[str, Any]]]) -> Dict[str, List[float]]:
        """
        Geriye uyumluluk için korunan eski analyze fonksiyonu.

        Args:
            last_price (float): Mevcut son fiyat
            pivots (dict): 'high_pivots' ve 'low_pivots' listelerini içeren sözlük

        Returns:
            dict: {'bullish_flips': [fiyat_seviyesi], 'bearish_flips': [fiyat_seviyesi]}
        """
        logger.warning("Eski analyze fonksiyonu kullanılıyor. Yeni analyze() fonksiyonunu kullanın.")

        high_pivots = pivots.get('high_pivots', [])
        low_pivots = pivots.get('low_pivots', [])

        if not high_pivots or not low_pivots:
            logger.debug("S/R Flip analizi için yeterli pivot verisi yok")
            return {'bullish_flips': [], 'bearish_flips': []}

        # Basit flip tespiti (eski mantık)
        bullish_flips = []
        bearish_flips = []

        # Son 5 yüksek pivotu kontrol et
        for r_pivot in high_pivots[-5:]:
            r_level = float(r_pivot['price'])
            price_distance = abs(last_price - r_level) / r_level
            if price_distance < self.tolerance_pct and last_price >= r_level:
                bullish_flips.append(r_level)

        # Son 5 düşük pivotu kontrol et
        for s_pivot in low_pivots[-5:]:
            s_level = float(s_pivot['price'])
            price_distance = abs(last_price - s_level) / s_level
            if price_distance < self.tolerance_pct and last_price <= s_level:
                bearish_flips.append(s_level)

        return {
            'bullish_flips': list(set(bullish_flips)),
            'bearish_flips': list(set(bearish_flips))
        }
